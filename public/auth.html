<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书授权页面</title>
</head>

<body>
    <script>
        // 获取URL中的参数
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');

        if (code) {
            // 向父窗口发送消息
            window.parent.postMessage({
                code: code,
                state: state
            }, window.location.origin);

            // 显示成功信息
            document.body.innerHTML = '<h2>授权成功，正在关闭窗口...</h2>';

        } else {
            // 向父窗口发送拒绝授权的消息
            window.parent.postMessage({
                state: state,
                error: 'access_denied'
            }, window.location.origin);

            document.body.innerHTML = '<h2>拒绝授权</h2>';
        }
    </script>
</body>

</html>