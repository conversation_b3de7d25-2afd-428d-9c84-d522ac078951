const cookieKey = `${location.hostname}.mushroom.session.id`
const domain = '.syounggroup.com'

const utils = {
    isSmallScreen() {
        return window.innerWidth < 1300;
    },
    isMobile() {
        return window.innerWidth < 768;
    },
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    timeFormate(date, fmt) {
        if (typeof date !== 'object') {
            if (date && date.toString().length === 10) { // 秒为单位的时间戳
                date = new Date(date * 1000)
            } else {
                date = new Date(date)
            }
        }
        var o = {
            'M+': date.getMonth() + 1, // 月份
            'd+': date.getDate(), // 日
            'h+': date.getHours(), // 小时
            'm+': date.getMinutes(), // 分
            's+': date.getSeconds(), // 秒
            'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
            S: date.getMilliseconds() // 毫秒
        }
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
        for (var k in o) { if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)) }
        return fmt
    },
    throttle(func, delay) {
        let lastTime = 0;
        return function (...args) {
            const now = Date.now();
            if (lastTime === 0 || now - lastTime >= delay) {
                lastTime = now;
                func.apply(this, args);
            }
        };
    },
    debounce(fn, wait) {
        let timer = null;
        return function (...args) {
            if (timer) {
                clearTimeout(timer);
            }
            timer = setTimeout(() => {
                fn.apply(this, args);
                timer = null;
            }, wait);
        };
    },

    setCookie(value) {
        const t = 100
        var oDate = new Date();
        oDate.setDate(oDate.getDate() + t);
        document.cookie = `${cookieKey}=${value};Path=/;expires=${oDate.toDateString()};domain=${domain}`
    },
    getCookie(key) {
        var arr1 = document.cookie.split("; ");//由于cookie是通过一个分号+空格的形式串联起来的，所以这里需要先按分号空格截断,变成[name=Jack,pwd=123456,age=22]数组类型；
        for (var i = 0; i < arr1.length; i++) {
            var arr2 = arr1[i].split("=");//通过=截断，把name=Jack截断成[name,Jack]数组；
            if (arr2[0] == (key || cookieKey)) {
                return decodeURI(arr2[1]);
            }
        }
    },
    removeCookie(key) {
        const cookieName = key || cookieKey;
        document.cookie = `${cookieName}=;Path=/;expires=Thu, 01 Jan 1970 00:00:01 GMT;domain=${domain}`;
    },

    generateOssImageUrl(baseUrl, width = 400, height = 400, noZip) {
        if (!baseUrl) {
            return ''
        }
        // 检查是否为GIF格式
        if (!baseUrl.toLowerCase().endsWith('.png')) {
            return baseUrl;
        }
        if (noZip) {
            return baseUrl
        }
        // 构建图片处理参数
        const processParams = `image/resize,w_${width},h_${height}`;

        // 检查URL是否已经包含参数
        const separator = baseUrl.includes('?') ? '&' : '?';
        // 返回带有处理参数的URL
        return `${baseUrl}${separator}x-oss-process=${processParams}`;
    },
    copyText(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style = 'position: absolute;left: -100px;top: -100px;width: 0;height: 0;overflow: hidden;'
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
    },
    downImg(imageUrl) {
        // 提取文件名
        const fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);

        // 使用 fetch 下载图片
        fetch(imageUrl)
            .then(response => response.blob())
            .then(blob => {
                const downloadUrl = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.setAttribute('download', fileName);
                document.body.appendChild(link);
                link.click();
                link.remove();
            })
            .catch(error =>
                window.open(imageUrl)
            );
    }
}

export default utils