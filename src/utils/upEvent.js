export default {
    install(Vue) {
        const PAGE_ENTER_TIME = Date.now()
        const upTime = 3000 // 上报间隔时间
        const appVersion = '1.0.0' // 读取版本号
        const api = (events) => Vue.prototype.$api.post('/tracker-service/event/batchReport', events)
        const report = new Vue.prototype.$utils.Report(appVersion, api, upTime, PAGE_ENTER_TIME)
        Vue.prototype.$upEvent = function (eventName, attrs) {
            report.$upEvent(eventName, attrs)
        }
    }
}