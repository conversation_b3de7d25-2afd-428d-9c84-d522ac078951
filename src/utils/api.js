import axios from 'axios'
import { Message } from 'element-ui';
import config from '../config'
import utils from './index'
import uploadBigFile from './bigFile'
export default {
  install(Vue) {
    const instance = axios.create({
      baseURL: config.BASE_URL,
      timeout: 400000,
      headers: {
        'X-Tenant-Id': config.TENANT_ID,
        'X-Ext-Tenant-Id': config.EXT_TENANT_ID
      }
    });

    instance.interceptors.request.use(config => {
      const token = utils.getCookie()
      if (token) {
        if (token.length > 180) {
          config.headers['X-User-Token'] = token
        } else {
          config.headers['X-Session-Id'] = token
        }
      }
      return config
    })

    instance.interceptors.response.use(function (response) {
      if (!response.data.code) {
        return response.data
      }
      if (response.data.code === '400') {
        Vue.prototype.$goLogin()
        return Promise.reject()
      }
      if (response.data.code === '401') {
        window.$router.push('/')
        Message.error(response.data.msg)
        return Promise.reject(response.data.msg)
      }
      if (response.data.code === '421') {
        const msg = '附件上传功能仅限公司内网使用，暂不支持外网访问'
        Message.error(msg)
        return Promise.reject(msg)
      }
      if (response.data.code !== '0') {
        Message.error(response.data.msg)
        return Promise.reject(response.data.msg)
      }
      if (response.config.headers.retHeader) {
        return response
      }
      return response.data.data;
    }, function (error) {
      return Promise.reject(error);
    });

    Vue.prototype.$api = instance
    Vue.prototype.$baseURL = config.BASE_URL
    // 上传图片
    Vue.prototype.$uploadFile = async function (file) {
      const formData = new FormData();
      formData.append("file", file);

      const startTime = Date.now();

      const res = await this.$api.post("/file/api/file/upload", formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          const currentTime = Date.now();
          const elapsedTime = ((currentTime - startTime) / 1000).toFixed(2); // 秒
          console.log(`上传进度: ${percentCompleted}%`);
          console.log(`已用时间: ${elapsedTime}秒`);
        }
      });

      const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
      console.log(`总上传时间: ${totalTime}秒`);

      return res;
    }
    Vue.prototype.$uploadBigFile = uploadBigFile.bind(Vue.prototype)
  }

}


