import { Message } from 'element-ui';

import config from "../config";


const urlConfig = {
    clientId: config.appId,
    redirectUri: `${window.location.origin}/auth.html`,
    state: '123456',
    scope: ['docs:document.content:read', 'drive:drive:readonly', 'wiki:wiki:readonly', 'docx:document:readonly']
};


// 飞书登录
export function requestAuthCode() {
    if (window.tt) {
        return getFeishuAuthCode()
    }
    return getWebCode(false)
}

function getFeishuAuthCode() {
    return new Promise((resolve, reject) => {
        window.tt.requestAuthCode({
            appId: config.appId,
            success: info => {
                resolve({ code: info.code })
            },
            fail: error => {
                this.$message('error')
                this.$message(JSON.stringify(error))
                reject(error)
            }
        })
    })
}






// 飞书授权配置
export function auth() {
    // if (window.tt) {
    //     return getFeishuCode()
    // } else {
    return getWebCode(true)
    // }
}

// 跳转到飞书对应人员
export function goFeishuUser(openId) {
    const url = `https://applink.feishu.cn/client/chat/open?openId=${encodeURIComponent(openId)}`;
    window.open(url)
}


function getFeishuCode() {
    if (window.tt.requestAccess) {
        return new Promise((resolve, reject) => {
            window.tt.requestAccess({
                appID: urlConfig.clientId, // 替换为你的应用的 App ID
                scopeList: urlConfig.scope, // 替换为需要用户授权的权限列表
                state: urlConfig.state, // 用于维护请求状态的附加字符串
                success: (res) => {
                    // 用户授权成功后返回授权码
                    const { code } = res;
                    // 将授权码发送到后端进行后续处理
                    resolve({ code, redirectUri: `${location.origin}/auth.html` });
                },
                fail: (error) => {
                    // 用户拒绝授权或者授权失败
                    const { errno, errString } = error;
                    console.error("授权失败，错误码:", errno, "错误信息:", errString);
                    reject("授权失败，错误码:", errno, "错误信息:", errString);
                },
            });
        })
    }
}


function getWebCode(hasRoot = true) {
    return new Promise((resolve, reject) => {
        // 构建授权URL
        const authUrl = `https://accounts.feishu.cn/open-apis/authen/v1/authorize?` +
            `client_id=${encodeURIComponent(urlConfig.clientId)}&` +
            `redirect_uri=${encodeURIComponent(urlConfig.redirectUri)}&` +
            `state=${encodeURIComponent(urlConfig.state)}&` +
            `scope=${encodeURIComponent(hasRoot ? urlConfig.scope.join(' ') : '')}`;

        // 保存原始body样式
        const originalBodyOverflow = document.body.style.overflow;

        // 创建全屏iframe覆盖层
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
        overlay.style.zIndex = '9999';
        overlay.style.display = 'flex';
        overlay.style.justifyContent = 'center';
        overlay.style.alignItems = 'center';

        // 禁用body滚动
        document.body.style.overflow = 'hidden';

        // 创建容器
        const container = document.createElement('div');
        container.style.position = 'relative';
        container.style.width = '610px';
        container.style.height = '590px';
        container.style.maxWidth = '94%';

        // 创建关闭按钮
        const closeButton = document.createElement('i');
        closeButton.className = 'el-icon-close';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '12px';
        closeButton.style.right = '12px';
        closeButton.style.width = '30px';
        closeButton.style.height = '30px';
        closeButton.style.padding = '0';
        closeButton.style.color = '#909399';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '50%';
        closeButton.style.cursor = 'pointer';
        closeButton.style.zIndex = '10001';
        closeButton.style.fontSize = '22px';
        closeButton.style.lineHeight = '30px';
        closeButton.style.textAlign = 'center';

        // 添加鼠标悬停效果
        closeButton.addEventListener('mouseover', () => {
            closeButton.style.color = '#606266';
        });
        closeButton.addEventListener('mouseout', () => {
            closeButton.style.color = '#909399';
        });

        // 创建iframe
        const iframe = document.createElement('iframe');
        iframe.src = authUrl;
        iframe.style.width = '100%';
        iframe.style.height = '100%';
        iframe.style.border = 'none';
        iframe.style.borderRadius = '8px';
        iframe.style.backgroundColor = 'white';

        // 添加到DOM
        container.appendChild(iframe);
        container.appendChild(closeButton);
        overlay.appendChild(container);
        document.body.appendChild(overlay);

        // 关闭按钮事件
        closeButton.addEventListener('click', () => {
            document.body.removeChild(overlay);
            // 恢复body原始样式
            document.body.style.overflow = originalBodyOverflow;
            reject(new Error('用户关闭了授权窗口'));
        });

        // 监听消息
        window.addEventListener('message', function handleMessage(event) {
            // 验证消息来源
            if (event.origin !== window.location.origin || !event.data.state) {
                return;
            }

            // 获取授权码
            const code = event.data.code;
            if (code) {
                document.body.removeChild(overlay);
                // 恢复body原始样式
                document.body.style.overflow = originalBodyOverflow;
                window.removeEventListener('message', handleMessage);
                resolve({ code, redirectUri: urlConfig.redirectUri });
            } else {
                document.body.removeChild(overlay);
                // 恢复body原始样式
                document.body.style.overflow = originalBodyOverflow;
                window.removeEventListener('message', handleMessage);
                reject(new Error('用户拒绝授权'));
                Message.error('用户拒绝授权');
            }
        }, false);
    });
}