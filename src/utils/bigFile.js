async function getOSSClient({ isPrivate } = { isPrivate: false }) {
    const refreshSTSTokenInterval = 300000
    const data = await this.$api.post('/file-service/api/file/fetchUploadToken', { durationSeconds: refreshSTSTokenInterval / 1000 })
    getOSSClient.resourcePrefix = data.resourcePrefix
    const ossClient = new window.OSS(
        {
            endpoint: data.domain,
            cname: true,
            // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
            region: data.region,
            // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
            accessKeyId: data.accessKeyId,
            accessKeySecret: data.accessKeySecret,
            // 从STS服务获取的安全令牌（SecurityToken）。
            stsToken: data.securityToken,
            refreshSTSTokenInterval: refreshSTSTokenInterval - 1000,
            // 填写Bucket名称。
            bucket: isPrivate ? data.privateBucketName : data.bucketName
        }
    )

    return {
        ossClient, domain: data.domain
    }
}

async function uploadBigFile(file, config) {
    const { ossClient, domain } = await getOSSClient.call(this, config)
    // 如果用户自己有定义文件路径，优先使用用户定义的，mushroom文件管理系统使用-火鸟推荐方案
    const type = file.name.split('.').pop()
    let name = `${Math.random()}_${file.uid}_${Date.now()}.${type}`
    const result = await ossClient.multipartUpload(`${getOSSClient.resourcePrefix}/${name}`, file, {
        headers: {
            'x-oss-object-acl': config.isPrivate ? 'private' : 'public-read',
            'Content-Disposition': `attchment;filename=${encodeURI(name)}`
        },
        progress: (p, cpt, res) => {
            if (config.onProgress) {
                config.onProgress(Math.floor(p * 100), cpt, res, ossClient)
            }
        }
    })
    return `${domain}/${result.name}`
}

export default uploadBigFile