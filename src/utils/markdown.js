import MarkdownIt from 'markdown-it'
import Prism from 'prismjs'


const md = new MarkdownIt({
    html: true,
    linkify: true,
    typographer: true,
    breaks: true,
    highlight
});


md.renderer.rules.image = function (tokens, idx, options, env, self) {
    const token = tokens[idx];
    const src = token.attrGet('src');
    const alt = token.content;
    return `<img src="${src}" alt="${alt}" class="markdown-img">`;
};

md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
    const token = tokens[idx];
    token.attrSet('target', '_blank');
    token.attrSet('rel', 'noopener noreferrer');
    return self.renderToken(tokens, idx, options);
};

function highlight(str, lang) {
    if (lang && Prism.languages[lang]) {
        try {
            return `<pre class="language-${lang}"><code>${Prism.highlight(str, Prism.languages[lang], lang)}</code></pre>`;
        } catch (e) {
            console.error(e);
        }
    }
    return `<pre class="language-text"><code>${md.utils.escapeHtml(str)}</code></pre>`;
}


function render(content) {
    try {
        const html = md.render(content)
        return `<div class="markdown-body">${html}</div>`
    } catch (e) {
        console.error('Markdown渲染错误:', e);
        return '';
    }
}

function initImgClick(that) {
    if (!that || !that.$el || typeof that.$preview !== 'function') {
        console.warn('initImgClick: 缺少必要的参数或方法');
        return;
    }

    that.$el.addEventListener('click', (e) => {
        const target = e.target;
        if (target.tagName === 'IMG' && target.classList.contains('markdown-img')) {
            that.$preview(target.src);
        }
    });
}



export default {
    initImgClick,
    render
};