export const isChrome = () => {
    // 排除其他浏览器的特征检测
    if (
        // 排除Microsoft Edge
        (navigator.userAgent.indexOf('Edg') !== -1) ||
        // 排除Safari
        (navigator.userAgent.indexOf('Safari') !== -1 &&
            navigator.userAgent.indexOf('Chrome') === -1) ||
        // 排除Firefox
        (navigator.userAgent.indexOf('Firefox') !== -1) ||
        // 排除Opera
        (navigator.userAgent.indexOf('OPR') !== -1) ||
        // 排除Yandex
        (navigator.userAgent.indexOf('YaBrowser') !== -1)
    ) {
        return false;
    }

    // 检测Chrome特征
    const isChromium = window.chrome;
    const winNav = window.navigator;
    const vendorName = winNav.vendor;
    const isOpera = winNav.userAgent.indexOf('OPR') !== -1;
    const isIEedge = winNav.userAgent.indexOf('Edg') !== -1;

    return isChromium !== null &&
        typeof isChromium !== 'undefined' &&
        vendorName === 'Google Inc.' &&
        !isOpera &&
        !isIEedge;
} 