<template>
    <cp-agent :config="config">
        <template slot-scope="agent">
            <div class="page-agent-xhx">
                <div class="title">抖音达播推荐助手</div>
                <div class="desc">围绕抖音达播打造的AI智能体，祝你轻松选号</div>
                <div class="btns">
                    <el-button v-for="(it, i) in btns" :key="i" size="small" @click="onBtnClick(agent, i, it)"
                        :type="activeIndex === i ? 'primary' : ''">
                        {{ it.label }}
                    </el-button>
                </div>
                <div class="content-area" v-if="activeItem">
                    <div>
                        <div class="content-title">{{ activeItem.title }}</div>
                        <div class="content-desc">{{ activeItem.desc }}</div>
                    </div>
                </div>
            </div>
        </template>
    </cp-agent>
</template>

<script>
export default {
    data() {
        return {
            activeIndex: 0
        }
    },
    computed: {
        activeItem() {
            return this.btns[this.activeIndex]
        },
        config() {
            return {}
        },
        btns() {
            return [
                {
                    label: '达播带货信息',
                    title:'',
                    temp: {
                        value: '帮我获取这个抖音达播的带货信息：${url}；',
                        params: [
                            {
                                default_text: '',
                                placeholder: '达人抖音号或主页链接',
                                value: 'url'
                            }
                        ]
                    },
                    desc: `输入达人抖音号或主页链接，获取带货数据和集团数据
目前一次只支持获取一个达人的带货数据。`,
                },
//                 {
//                     label: '评分模型列表',
//                     title:'',
//                     desc: `查看在KOL系统中配置的达播评分模型
// 如果没有评分模型，请在[HERMES-KOL系统-技术开始表演]中，找值班人员配置。`,
//                     temp: {
//                         value: `获取已有的评分模型`,
//                         params: []
//                     }
//                 },
                {
                    label: '给达播评分',
                    title:'',
                    desc: `输入达人抖音号或主页链接，分析达人带货数据并评分
目前一次只支持给一个达人评分。`,
                    temp: {
                        value: `帮我给这个抖音达播：\${url}，按照这个【\${info}】评分模型进行评分`,
                        params: [
                            {
                                default_text: '',
                                placeholder: '达人抖音号或主页链接',
                                value: 'url'
                            },
                            {
                                default_text: '',
                                placeholder: '评分模型名',
                                type: 'select',
                                options:'/ai-main-app/api/liveTalentRecommend/getScoreModelList',
                                value: 'info'
                            }
                        ]
                    }
                }
            ]
        }
    },
    methods: {
        onBtnClick(agent, i, it) {
            this.activeIndex = i
            agent.$goAgent({
                interactionType: 'PROMPT_IN',
                ...it.temp
            })
        }
    },
    mounted() {
    }
}
</script>

<style lang="less">
.page-agent-xhx {
    padding: 12px;
    box-sizing: border-box;

    .title {
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .desc {
        color: #6E768C;
        margin-bottom: 18px;
        font-size: 12px;
    }

    .btns {
        margin-bottom: 18px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .el-button {
            margin: 0;
            flex-basis: calc(33.33% - 8px);
            min-width: calc(33.33% - 8px);
        }
    }

    .content-area {
        background: #F3F5F8;
        border-radius: 8px;
        padding: 24px 18px;
        font-size: 14px;

        .content-title {
            margin-bottom: 8px;
        }

        .content-desc {
            color: #6E768C;
            line-height: 1.8;
            white-space: pre-wrap;

            .row {
                margin-bottom: 12px;
            }

        }
    }
}
</style>