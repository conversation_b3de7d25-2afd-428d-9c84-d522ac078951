<template>
    <cp-agent :config="config">
        <template slot-scope="agent">
            <div class="page-agent-xhx">
                <div class="title">小红书市场助手</div>
                <div class="desc">围绕小红书平台打造的AI智能体，助你轻松玩转小红书。</div>
                <div class="btns">
                    <el-button v-for="(it, i) in btns" :key="i" size="small" @click="onBtnClick(agent, i, it)"
                        :type="activeIndex === i ? 'primary' : ''">
                        {{ it.label }}
                    </el-button>
                </div>
                <div class="content-area" v-if="activeItem">
                    <div>
                        <div class="content-title">{{ activeItem.title }}</div>
                        <div class="content-desc">{{ activeItem.desc }}</div>
                    </div>
                </div>
            </div>
        </template>
    </cp-agent>
</template>

<script>
export default {
    data() {
        return {
            activeIndex: 0
        }
    },
    computed: {
        activeItem() {
            return this.btns[this.activeIndex]
        },
        config() {
            return {}
        },
        btns() {
            return [
                {
                    label: '获取作品数据',
                    title:'输入达人作品链接，批量获取达人互动数据',
                    temp: {
                        value: '帮我获取这些小红书作品数据：${url}；',
                        params: [
                            {
                                default_text: '',
                                placeholder: '作品链接',
                                value: 'url'
                            }
                        ]
                    },
                    desc: `发送小红书达人的作品链接给ZEUS,
返回该作品最新的互动数据。
单次最多获取30个作品链接,
超出30个作品链接需分多次发送。`,
                },
                {
                    label: '获取达人数据',
                    title:'输入达人链接，批量获取达人数据',
                    desc: `发送小红书达人主页链接给ZEUS,
返回该达人最新的粉丝画像，互动，费用等数据。
单次最多获取30个达人链接,
超出30个达人需分多次发送。`,
                    temp: {
                        value: '帮我获取这些小红书达人数据：${url}；',
                        params: [
                            {
                                default_text: '',
                                placeholder: '达人主页链接',
                                value: 'url'
                            }
                        ]
                    }
                },
//                 {
//                     label: '获取评论数据',
//                     title:'输入达人作品链接，获取作品评论，输出评论词云',
//                     desc: `发送小红书达人主页链接给ZEUS,
// 返回该作品首页的20条评论
// 单次最多获取10个达人链接,
// 仅支持单个作品链接,
// 多个作品链接需分多次发送。`,
//                     temp: {
//                         value: '帮我获取这个作品的评论：${url}；',
//                         params: [
//                             {
//                                 default_text: '',
//                                 placeholder: '作品链接',
//                                 value: 'url'
//                             }
//                         ]
//                     }
//                 },
                {
                    label: '达人账号分析',
                    title:'分析达人的画像和风格以及和达人的匹配程度',
                    desc: `发送小红书达人主页链接给ZEUS,
返回达人的画像，带货类目，合作品牌等分析
单次仅支持单个达人分析,
多个作达人需分多次发送。`,
                    temp: {
                        value: `帮我分析这个小红书达人与品牌的匹配程度
- 小红书达人主页链接:\${url}
- 品牌信息：
    1. 产品名称：\${params1}
    2. 价格定位: \${params2}
    3. 内容需求类型：\${params3}
    4. 类目：\${params4}
`,
                        params: [
                            {
                                default_text: '',
                                placeholder: '请输入达人主页链接',
                                value: 'url'
                            },
                            {
                                default_text: ``,
                                placeholder: '请输入产品名称',
                                value: 'params1'
                            },
                            {
                                default_text: ``,
                                placeholder: '请输入价格定位',
                                value: 'params2'
                            },
                            {
                                default_text: ``,
                                placeholder: '请选择内容需求类型',
                                value: 'params3',
                                type: 'select',
                                options:'/ai-main-app/api/xhsMarketCampaign/getScoreModelList',
                           
                            },
                            {
                                default_text: ``,
                                placeholder: '请输入类目',
                                value: 'params4',
                                }
                        ]
                    }
                },
                {
                    label: '作品内容分析',
                    title:'输入单个达人作品链接，分析作品结构',
                    desc: `发送小红书达人作品链接给ZEUS,
返回作品的4T分析，作品结构等分析
单次仅支持单个作品分析,
多个作作品需分多次发送。`,
                    temp: {
                        value: '帮我分析这个作品内容：${url}；',
                        params: [
                            {
                                default_text: '',
                                placeholder: '作品链接',
                                value: 'url'
                            }
                        ]
                    }
                },
            ]
        }
    },
    methods: {
        onBtnClick(agent, i, it) {
            this.activeIndex = i
            agent.$goAgent({
                interactionType: 'PROMPT_IN',
                ...it.temp
            })
        }
    },
    mounted() {}
}
</script>

<style lang="less">
.page-agent-xhx {
    padding: 12px;
    box-sizing: border-box;

    .title {
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .desc {
        color: #6E768C;
        margin-bottom: 18px;
        font-size: 12px;
    }

    .btns {
        margin-bottom: 18px;
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .el-button {
            margin: 0;
            flex-basis: calc(33.33% - 8px);
            min-width: calc(33.33% - 8px);
        }
    }

    .content-area {
        background: #F3F5F8;
        border-radius: 8px;
        padding: 24px 18px;
        font-size: 14px;

        .content-title {
            margin-bottom: 8px;
        }

        .content-desc {
            color: #6E768C;
            line-height: 1.8;
            white-space: pre-wrap;

            .row {
                margin-bottom: 12px;
            }

        }
    }
}
</style>