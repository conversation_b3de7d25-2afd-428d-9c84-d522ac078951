<template>
    <div class="cp-send-input" :class="{ isCompleted, 'disabled': !sceneFeatureSwitches.enableInput }"
        @paste="handlePaste">
        <scene @flashMsgList="flashMsgList"></scene>
        <div class="cp-send-input-wrap">
            <agent :isShow.sync="showAgent" @close="setAgentShow(false)" @addTemp="addTemp" ref="agent"
                @flashMsgList="flashMsgList" />
            <file-list ref="fileList" :list="fileList"></file-list>
            <div class="text-temp-wrap hover-scroll">
                <textTemp v-if="showTemp" :temp="value.temp" ref="textTemp" @enter="sendMsg" @change="setTextValue"
                    @AT="setAgentShow(true)" :placeholder="sceneFeatureSwitches.sceneInputPlaceHolder" />
            </div>
            <div class="box-bottom">
                <div class="left">
                    <el-tooltip effect="dark" content="切换AI大模型" placement="top"
                        v-if="sceneFeatureSwitches.enableLargeModelSwitch">
                        <select-model v-model="selectModel"></select-model>
                    </el-tooltip>
                    <el-tooltip effect="dark" content="开启后会根据搜索到的互联网内容进行回答" placement="top"
                        v-if="sceneFeatureSwitches.enableWebSearchSwitch">
                        <div class="net" :class="{ active: enableSearch }" @click="enableSearch = !enableSearch">
                            <i class="iconfont icon-shequ"></i>
                            <span>联网搜索</span>
                        </div>
                    </el-tooltip>
                </div>
                <div class="right">
                    <el-tooltip v-if="sceneFeatureSwitches.enableFileUpload" effect="dark"
                        content="上传文件,支持Word、PDF、Excel、PPT、txt，单个文件不大于100M" placement="top">
                        <i class="tool iconfont icon-dakaiwenjianweizhi" @click="handleFileUpload('file')"></i>
                    </el-tooltip>
                    <el-tooltip v-if="sceneFeatureSwitches.enableImageUpload" effect="dark"
                        :content="isDisabledImg ? disabledImgText : 'Ctrl+v粘贴截图或手动上传图片，单张图片不大于10M'" placement="top">
                        <i class="tool iconfont icon-tuxiangshengcheng" :class="{ disabled: isDisabledImg }"
                            @click="handleFileUpload('image')"></i>
                    </el-tooltip>
                    <el-tooltip v-if="sceneFeatureSwitches.enableSkillSwitch" effect="dark" content="选择预设的AI技能"
                        placement="top">
                        <i class="tool iconfont icon-at" @click.stop="setAgentShow(true)"></i>
                    </el-tooltip>

                    <div class="split"></div>
                    <el-tooltip v-if="isCompleted" effect="dark" :content="sendBtnTips" placement="top"
                        :disabled="canSendMsg">
                        <i class="sendIcon iconfont icon-fasong1" :class="{ active: canSendMsg }" @click="sendMsg"></i>
                    </el-tooltip>
                    <i v-else class="sendIcon iconfont icon-stop-circle-full" :class="{ active: true }"
                        @click="stopMsg"></i>
                </div>

            </div>
        </div>

    </div>
</template>

<script>
import textTemp from './textTemp.vue';
import selectModel from './selectModel.vue';
import fileList from './fileList.vue';
import agent from './agent.vue';
import scene from './input/scene.vue';
import axios from 'axios';
import { mapState } from 'vuex'

export default {
    components: {
        textTemp,
        selectModel,
        fileList,
        agent,
        scene
    },
    props: {
        value: {}
    },
    data() {
        return {
            showAgent: false,
            textValue: '',
            selectModel: localStorage.getItem('selectModel') || 'GPT',
            enableSearch: localStorage.getItem('enableSearch') === 'true',
            showTemp: true,
            source: null,
            fileList: [],
            callState: {}
        }
    },
    computed: {
        ...mapState(['sceneFeatureSwitches', 'isCompleted']),
        isDisabledImg() {
            return this.sceneFeatureSwitches.enableLargeModelSwitch && ['DEEPSEEK'].includes(this.selectModel)
        },
        disabledImgText() {
            return `DeepSeek不支持发送图片`
        },
        attachmentsHasImg() {
            return this.fileList.some(it => it.type === 'IMAGE')
        },
        conversationId() {
            return this.$route.query.conversationId
        },
        userInfo() {
            return this.$store.state.userInfo
        },
        hasFileLoading() {
            return this.fileList.some(it => it.loading)
        },
        canSendMsg() {
            if (this.hasFileLoading) {
                return false
            }
            if (!this.textValue && !this.fileList.length) {
                return false
            }
            if (!this.sceneFeatureSwitches.allowEmptyAttachmentSend && !this.textValue) {
                return false
            }
            return true
        },
        sendBtnTips() {
            if (this.hasFileLoading) {
                return '文件上传中'
            }
            if (!this.textValue) {
                return '请输入您的问题'
            }
            return ''
        },
    },
    watch: {
        selectModel(newVal) {
            localStorage.setItem('selectModel', newVal);
        },
        enableSearch(newVal) {
            localStorage.setItem('enableSearch', newVal);
        }
    },
    methods: {
        handlePaste(event) {
            const clipboardData = event.clipboardData || window.clipboardData;

            // 检查是否有文件
            if (clipboardData.files && clipboardData.files.length > 0) {
                event.preventDefault();
                if (!this.checkFileTypes(clipboardData.files)) {
                    return
                }
                this.dealFile([...clipboardData.files]);
                return
            }

            // 检查是否是链接
            const text = clipboardData.getData('text');
            this.pasteLinks(event, text)

        },
        checkFileTypes(files) {
            const fileTypes = [...files].map(file => this.$refs.fileList.getFileType(file))
            if (!this.sceneFeatureSwitches.enableFeishuDocSupport && (
                fileTypes.includes('FEISHU_DOC') ||
                fileTypes.includes('FEISHU_EXCEL') ||
                fileTypes.includes('FEISHU_WIKI')
            )) {
                return false
            }
            if (!this.sceneFeatureSwitches.enableWebLinkSupport && fileTypes.includes('WEB')) {
                return false
            }
            if (!this.sceneFeatureSwitches.enableImageUpload && fileTypes.includes('IMAGE')) {
                return false
            }
            if (!this.sceneFeatureSwitches.enableFileUpload && (
                fileTypes.includes('AUDIO') ||
                fileTypes.includes('VIDEO') ||
                fileTypes.includes('WORD') ||
                fileTypes.includes('PPT') ||
                fileTypes.includes('EXCEL') ||
                fileTypes.includes('PDF') ||
                fileTypes.includes('TXT') ||
                fileTypes.includes('RAR')
            )) {
                return false
            }
            return true
        },
        pasteLinks(event, text) {
            // 使用正则表达式分割文本（按空格和换行）
            const links = text.split(/[\s\n]+/).filter(link => link.trim());

            // 检查是否所有分割后的内容都是有效的http链接
            const isValidHttpLink = (url) => /^https?:\/\/.+/.test(url);
            const allValidLinks = links.length > 0 && links.every(isValidHttpLink);

            if (allValidLinks) {
                if (!this.checkFileTypes(links)) {
                    return
                }
                event.preventDefault();
                // 将所有有效链接作为附件上传
                links.forEach(link => {
                    this.dealFile(link);
                });
                return;
            }
        },
        retryMsg(msg) {
            // 实时发送更新
            this.msgApi({
                conversationId: this.conversationId,
                retryMessageId: msg.id,
                enableSearch: this.enableSearch,
                model: this.selectModel,
            })
        },
        ssePending() {
            this.msgApi({ conversationId: this.conversationId }, '', true)
        },
        handleFileUpload(type) {
            if (type === 'image' && this.isDisabledImg) {
                return
            }
            const input = document.createElement('input');
            input.type = 'file';
            if (type === 'image') {
                input.accept = 'image/*';
            } else if (type === 'file') {
                input.accept = '.docx,.doc,.pptx,.ppt,.xls,.xlsx,.pdf,.txt';
            }
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.dealFile(file);
                }
            };
            input.click();
        },
        async dealFile(data) {
            this.$refs.fileList.addFile(data)
        },
        setAgentShow(v) {
            this.showAgent = v
            this.$refs.agent.focus()
        },
        setIsCompleted(v) {
            this.$store.commit('setIsCompleted', v)
        },
        async stopMsg() {
            await this.$api.get(`/ai-main-app/api/chat/stream/stop`, { params: { conversationId: this.conversationId } })
            this.setIsCompleted(true)
            // this.$emit('sendMsg', {
            //     role: 'ASSISTANT',
            //     id: this.sessionInfo.messageId,
            // });
            this.flashMsgList()  // 刷新列表消息，获取暂停时的服务端发送的消息

        },
        setTextValue() {
            this.textValue = this.$refs.textTemp.$getValue()
        },
        getTextValue() {
            this.setTextValue()
            return this.textValue
        },
        clearMsgContentById(id) {
            if (!id) {
                return
            }
            this.$emit('sendMsg', {
                content: '',
                reasonContent: '',
                id,
            });
        },
        msgApi(msgData, content, isSsePending) {
            if (!isSsePending && this.isCompleted === false) {
                return
            }
            // 清空消息
            this.clearMsgContentById(msgData.retryMessageId)
            this.showAgent = false
            this.setIsCompleted(false)
            this.source = axios.CancelToken.source();
            this.msgCount = 0
            this.$api.post('/ai-main-app/api/chat/stream/completion', {
                sceneCode: this.sceneFeatureSwitches.sceneCode,
                appCode:this.sceneFeatureSwitches.appCode,
                ...msgData,
            }, {
                cancelToken: this.source.token,
                responseType: 'text',
                headers: {
                    'Accept': 'text/event-stream'
                },
                onDownloadProgress: (progressEvent) => {
                    this.onDownloadProgress(progressEvent, content, msgData)
                }
            }).catch(error => {
                this.setIsCompleted(true)
            })
        },
        // 主动调用
        $sendMsg({ content, attachments = [] }) {
            this.$emit('sendMsg', {
                role: 'USER',
                content: content,
                avatarUrl: this.userInfo.avatarUrl,
                attachments: attachments,
                type: 'LLM'
            });

            setTimeout(() => {
                this.$store.dispatch('initList')
            }, 1000)

            this.msgApi({
                content,
                model: this.selectModel,
                enableSearch: this.enableSearch,
                conversationId: this.conversationId,
                attachments: attachments.map(it => ({ id: it.id }))
            }, content)
        },
        sendMsg() {
            if (this.isCompleted === false) {
                return
            }
            // 模型不支持图片
            if (this.attachmentsHasImg && this.isDisabledImg) {
                this.$message.error(this.disabledImgText)
                return
            }
            // 文件上传中
            if (!this.canSendMsg) {
                return
            }

            const content = this.getTextValue()
            const attachments = this.fileList.map(it => ({ ...it }))
            this.$refs.textTemp.$clearValue();
            this.fileList = []
            this.$sendMsg({ content, attachments })

        },
        onDownloadProgress(progressEvent, content, msgData) {
            this.setIsCompleted(false)
            this.msgCount++
            const responseText = progressEvent.currentTarget.responseText;
            const lines = responseText.split('data:').filter(_ => _).map(it => JSON.parse(it.trim()))
            const sessionInfo = lines.find(line => line.conversationId)
            const hasDone = lines.find(line => line.content === '[DONE]')
            if (!sessionInfo) {
                return
            }
            this.sessionInfo = sessionInfo;

            if (this.msgCount === 1) {
                this.$emit('startReceiveMsg') // 滚动到底部
                this.callState = {}
            }

            // 进入页面，刷新标题
            if (!this.conversationId) {
                this.updateTitle(this.sessionInfo.conversationId, content)
                this.$router.replace({
                    query: {
                        nt: '1',   // 回复消息的不要刷新页面
                        conversationId: this.sessionInfo.conversationId
                    }
                });
            }

            this.msgStateCall('startUpdate', lines, this.flashMsgList)

            if (hasDone) {
                this.msgStateCall('endUpdate', lines, this.flashMsgList)
                this.setIsCompleted(true)
            }

            // 实时发送更新
            this.$emit('sendMsg', {
                content: '',
                reasonContent: '',
                ...this.getMergeMsg(lines),
                role: 'ASSISTANT',
                conversationId: this.sessionInfo.conversationId,
                id: this.sessionInfo.messageId,
                replyId: this.sessionInfo.replyId,
                type: 'LLM'
            });
        },
        flashMsgList() {
            setTimeout(() => {
                this.$emit('flashMsgList')
            }, 0);
        },
        // 发现数据状态时，进行对应回调，用来处理消息数据
        msgStateCall(stateKey, lines, call) {
            const hasKey = lines.find(it => it[stateKey])
            if (hasKey && !this.callState[stateKey]) {
                this.callState[stateKey] = true
                call()
            }
        },
        async updateTitle(conversationId, content) {
            const res = await this.$api.post(`/ai-main-app//api/chat/summaryTitle`, {
                "content": content,
                "id": conversationId
            })
            this.$store.dispatch('initList')
        },
        getMergeMsg(lines) {
            // 合并所有对象的键值
            const mergedObject = {};

            lines.forEach(line => {
                Object.keys(line).forEach(key => {
                    if (line[key] === '[DONE]' || line.type !== 'LLM') {
                        return
                    }
                    if (mergedObject[key]) {
                        mergedObject[key] += line[key];
                    } else {
                        mergedObject[key] = line[key];
                    }
                });
            });
            return mergedObject;
        },
        addTemp(tempValue) {
            this.showTemp = false
            const result = []
            let currentText = tempValue.value
            let lastIndex = 0

            // 使用正则表达式匹配 ${} 中的变量
            const regex = /\${([^}]+)}/g
            let match

            while ((match = regex.exec(currentText)) !== null) {
                const fullMatch = match[0] // 完整的 ${xxx} 匹配
                const variableName = match[1] // 变量名

                // 在 params 中查找对应的变量
                const param = tempValue.params.find(p => p.value === variableName)

                if (param) {
                    // 添加 ${} 之前的文本
                    if (match.index > lastIndex) {
                        result.push({
                            key: variableName,
                            type: 'text',
                            value: currentText.substring(lastIndex, match.index)
                        })
                    }
                    // 添组件
                    if (param.type === 'select') {
                        result.push({
                            key: variableName,
                            type: 'select',
                            value: param.default_text,
                            placeholder: param.placeholder,
                            options: param.options
                        })
                    } else {
                        result.push({
                            key: variableName,
                            type: 'tag',
                            value: param.default_text,
                            placeholder: param.placeholder
                        })
                    }


                    lastIndex = match.index + fullMatch.length
                }
            }

            // 添加最后的文本部分
            if (lastIndex < currentText.length) {
                result.push({
                    type: 'text',
                    value: currentText.substring(lastIndex)
                })
            }

            setTimeout(() => {
                this.value.temp = result
                this.showTemp = true
            }, 0)
        }
    },
    beforeDestroy() {
        if (this.source) {
            this.source.cancel('用户取消了请求');
        }
    }
}
</script>


<style lang="less">
.cp-send-input {
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    &.disabled {
        pointer-events: none;
        opacity: 0.5;
    }

    .cp-send-input-wrap {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        min-height: 120px;
        max-height: 300px;
        padding-top: 12px;
        padding-bottom: 12px;

        &>* {
            padding-left: 12px;
            padding-right: 12px;

            &.agent {
                padding: 0;
            }
        }

    }

    .text-temp-wrap {
        flex: 1;
        overflow: auto;
        padding-bottom: 8px;
        white-space: pre-wrap;
        word-break: break-word;
        word-wrap: break-word;
    }

    .box-bottom {
        display: flex;
        gap: 8px;
        align-items: center;
        justify-content: space-between;
        line-height: 32px;

        &>.left {
            display: flex;
            gap: 8px;

            .net {
                cursor: pointer;
                height: 32px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                gap: 4px;
                border-radius: 4px;
                border: 1px solid #DCDFE6;
                padding: 0 8px;
                font-size: 12px;
                color: #606266;
                white-space: nowrap;
                overflow: hidden;

                .icon-hulianwangzhuanchang {
                    font-size: 16px;
                }

                &.active {
                    border: 1px solid #00B259;
                    color: #00B259;

                    i {
                        color: #00B259;
                    }
                }
            }
        }

        &>.right {
            display: flex;
            align-items: center;
            gap: 12px;


            .split {
                width: 2px;
                border-radius: 100px;
                height: 22px;
                background-color: #E6E8EB;

            }

            i.disabled {
                color: #9EA4B2;
            }

            .tool {
                color: #5E5E5E;
                font-size: 18px;
                cursor: pointer;
            }

            .sendIcon {
                font-size: 18px;
                background-color: #F3F5F8;
                width: 32px;
                height: 32px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #9EA4B2;
                cursor: pointer;

                &.active {
                    background-color: #E0F2E9;
                    color: #00B55B;
                }
            }
        }

        .el-select {
            width: 108px;
        }
    }
}
</style>