<template>
    <div class="cp-msg-list-wrap" :class="{ 'open-share': openShare }">
        <div class="scroll-bottom" v-show="showScrollBottom">
            <i class="iconfont el-icon-arrow-down" @click="scrollToBottom(true)"></i>
        </div>
        <div class="cp-msg-list hover-scroll" ref="msgList">
            <div class="msg-list">
                <div class="msg-list-item-wrap" :class="[getMsgType(it)]" v-for="(it, i) in list"
                    :key="it.id || `user_${i}`" :ref="`msg-${i}`">
                    <systemMsg v-if="isSystemMsg(it)" :msg="it" />
                    <div v-else class="msg-list-item" :class="{ [it.role]: true, checked: it.checked }"
                        :data-role="it.role" @click="handleCheckboxChange(it)">
                        <el-checkbox v-if="openShare" :value="it.checked"
                            @change="handleCheckboxChange(it)"></el-checkbox>
                        <div class="avatar">
                            <img v-if="it.role === 'USER'" :src="it.avatarUrl || defaultUserHeader" alt="">
                            <img v-else src="@/assets/logo/渐变图标.png" alt="">
                        </div>
                        <div class="msg-list-right">
                            <div class="bubble" :class="[it.type]">
                                <think :msg="it" v-if="list.length - 1 === i && !isCompleted && it.role === 'ASSISTANT'"/>
                                <template v-if="!it.type || it.type === 'LLM'">
                                    <render-reason :msg="it"></render-reason>
                                    <render-content :msg="it"></render-content>
                                </template>
                                <render-other-box v-else :msg="it" @updateMsg="updateMsg"></render-other-box>
                                <file-list :list="it.attachments" :readOnly="true"></file-list>
                            </div>
                            <opt-list v-if="!openShare" :msg="it" :list="list" @reBuild="reBuild" @shareMsg="shareMsg"
                                :isLast="i == list.length - 1" @updateMsg="updateMsg"></opt-list>
                        </div>

                    </div>
                </div>
                <guessQuestion v-if="false && !openShare && isCompleted && sceneFeatureSwitches.enableGuessQuestion && conversationId" :msgList="list" @updateMsg="updateMsg" @sendMsg="sendMsg" @scrollToBottom="scrollToBottom(true,'smooth')"/>
            </div>
        </div>
        <div class="share-bottom" v-if="openShare">
            <div class="share-bottom-content">
                <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
                <div class="share-buttons">
                    <el-button type="primary" size="small" @click="copyShareLink">复制链接</el-button>
                    <el-button size="small" @click="cancelShare">取消</el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import fileList from './fileList.vue'
import utils from '@/utils'
import optList from './msg/opt.vue'
import think from './msg/think.vue'
import renderReason from './msg/reason.vue'
import renderOtherBox from './msg/otherBox.vue'
import renderContent from './msg/content.vue'
import systemMsg from './msg/systemMsg.vue'
import guessQuestion from './msg/guessQuestion.vue'
import { mapState } from 'vuex'

export default {
    components: {
        fileList,
        optList,
        think,
        renderReason,
        renderContent,
        systemMsg,
        renderOtherBox,
        guessQuestion
    },
    props: {
        openShare: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            list: [],
            canScrollBottom: true,
            showScrollBottom: false,
            selectAll: false
        }
    },
    computed: {
        ...mapState(['userInfo', 'isCompleted','sceneFeatureSwitches']),
        defaultUserHeader() {
            return this.userInfo.gender === '1' ? 'https://oss.syounggroup.com/static/file/zeus/user_logo/man_default.png' : 'https://oss.syounggroup.com/static/file/zeus/user_logo/woman_default.png'
        },
        conversationId() { 
            return this.$route.query.conversationId
        }
    },
    methods: {
        sendMsg(msg) {
            this.$emit('sendMsg', msg)
        },
        updateMsg() {
            this.initListData()
        },
        getMsgType(it) {
            if (this.isSystemMsg(it)) {
                return 'SYSTEM'
            }
            return it.role === 'USER' ? 'USER' : 'ASSISTANT'
        },
        isSystemMsg(it) {
            return ['LLM_BOUNDARY', 'SYSTEM_INFO'].includes(it.type)
        },
        shareMsg(msg) {
            this.$emit('update:openShare', true)
            msg.checked = true
            this.$nextTick(() => {
                const index = this.list.indexOf(msg)
                const msgRef = this.$refs[`msg-${index}`]
                if (msgRef && msgRef[0]) {
                    msgRef[0].scrollIntoView({ behavior: 'smooth', block: 'start' })
                }
            })
        },
        async reBuild(msg) {
            this.$emit('retryMsg', msg)
        },
        handleScroll() {
            const el = this.$refs.msgList;
            const distanceToBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
            this.showScrollBottom = distanceToBottom > 150;
        },
        setIsCompleted(v) {
            this.$store.commit('setIsCompleted', v)
        },
        async initListData() {
            this.setIsCompleted(true)
            const id = this.conversationId
            if (!id) {
                return
            }
            const res = await this.$api.get('/ai-main-app/api/conversation/getDetail', { params: { id } })
            this.list = res.messages.map(it => ({ reasonContent: '', checked: false, content: '', ...it }))
            this.$store.commit('setSceneFeatureSwitches', {
                ...res.conversation,
                ...res.conversation.sceneFeatureSwitches,
                sceneFeatureSwitches: undefined
            })
            if (res.ssePending) {
                this.setIsCompleted(false)
                this.$emit('ssePending')
                this.list[this.list.length - 1].isReasonExpanded = true
            }
            this.scrollToBottom(true, 'instant');
        },
        $addMsg(msg) {
            if (msg.role === 'USER') {
                this.list.push({
                    ...msg
                });
                this.scrollToBottom(true);
            } else {
                this.updateUserId(msg)
                const item = this.list.find(it => msg.id === it.id)
                if (item) {
                    Object.assign(item, msg)
                    setTimeout(() => {
                        // 最后收起思考过程
                        if (this.isCompleted === true) {
                            this.$set(item, 'isReasonExpanded', false)
                        }
                    }, 0)
                } else {
                    this.list.push({
                        content: '',
                        reasonContent: '',
                        isReasonExpanded: !this.isCompleted,
                        ...msg
                    });
                }
                this.scrollToBottom(false, 'instant');
            }
        },
        scrollToBottom(isShouldScroll, behavior = 'smooth') {
            const el = this.$refs.msgList;
            if (isShouldScroll) {
                setTimeout(() => {
                    el.scrollTo({
                        top: el.scrollHeight,
                        behavior
                    });
                }, 0);
                return
            }
            if (!this.showScrollBottom) {
                el.scrollTo({
                    top: el.scrollHeight,
                    behavior
                })
            }
        },
        scroll: utils.throttle(function (...args) {
            this.$refs.msgList.scrollTo(...args)
        }, 200),
        updateUserId(msg) {
            if (!msg.replyId) {
                return
            }
            const item = this.list.find(it => !it.id)
            if (item) {
                item.id = msg.replyId
            }
        },
        handleCheckboxChange(msg) {
            if (!this.openShare) {
                return
            }
            msg.checked = !msg.checked
        },
        handleSelectAll() {
            this.list.forEach(msg => msg.checked = this.selectAll);
        },
        async copyShareLink() {
            const selectedMessages = this.list.filter(msg => msg.checked);
            if (selectedMessages.length === 0) {
                this.$message.warning('请选择要分享的消息');
                return;
            }
            const messageIds = selectedMessages.map(msg => msg.id);
            const conversationId = this.conversationId
            const res = await this.$api.post('/ai-main-app/api/conversationShare/create', {
                conversationId,
                messageIds
            });

            const shareLink = `${window.location.origin}/share.html#/?id=${res.id}`;
            this.$myUtils.copyText(shareLink)
            this.$message.success('对话链接已复制')
            this.cancelShare();
        },
        cancelShare() {
            this.selectAll = false;
            this.list.forEach(msg => msg.checked = false);
            this.$emit('update:openShare', false);
        }
    },
    created() {
        this.initListData()
    },
    mounted() {
        this.$refs.msgList.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        this.$refs.msgList.removeEventListener('scroll', this.handleScroll);
    }
}
</script>

<style lang="less">
:root {
    --msgMaxWidth: 274px;
}

.cp-agent {
    --msgMaxWidth: 674px;
}

.cp-send-input,
.msg-system-boundary,
.cp-msg-list .msg-list,
.share-bottom .share-bottom-content {
    max-width: calc(100vw - var(--msgMaxWidth));
}

@media screen and (max-width: 800px) {

    .cp-send-input,
    .msg-system-boundary,
    .cp-msg-list .msg-list,
    .share-bottom .share-bottom-content {
        max-width: calc(100vw - 24px);
    }
}
</style>


<style lang="less" scoped>
.cp-msg-list-wrap {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    flex: 1;
    min-height: 0; // 关键属性：确保内容可以正确滚动

    &.open-share {
        padding-bottom: 64px;

        .scroll-bottom {
            bottom: 80px;
        }

        .cp-msg-list {
            .msg-list {
                box-sizing: border-box;
                width: 800px;

                .msg-list-item-wrap {
                    width: 100%;
                    display: inline-block;

                    .msg-list-item {
                        padding: 12px;
                        cursor: pointer;
                        border-radius: 8px;
                        max-width: 100%;
                        box-sizing: border-box;

                        .avatar {
                            display: none;
                        }

                        &.USER {
                            align-self: flex-start;
                            flex-direction: row;
                        }

                        &.checked {
                            background-color: #E6E8EB;
                        }

                        &:hover {
                            background-color: #E6E8EB;
                        }

                        .msg-list-right {
                            padding-bottom: 0;

                            .bubble {
                                border-radius: 0 16px 16px 16px;
                            }
                        }
                    }
                }


            }

        }

    }

    .scroll-bottom {
        position: absolute;
        right: 10px;
        bottom: 20px;
        left: calc(50% - 13px);
        z-index: 10;

        i {
            border: 1px solid #E6E8EB;
            background-color: #fff;
            font-size: 16px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10000px;
            box-sizing: border-box;
            cursor: pointer;
        }
    }

    .cp-msg-list {
        overflow-y: auto;
        color: #222;
        height: 100%;
        width: 100%;

        .msg-list {
            padding: 16px 0;
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin: 0 auto;
            box-sizing: border-box;
            width: 896px;

            .msg-list-item-wrap {
                width: 100%;
                display: flex;

                &.SYSTEM {
                    justify-content: center;
                }

                &.USER {
                    justify-content: flex-end;
                }
            }

            .msg-list-item {
                display: flex;
                gap: 12px;
                animation: fadeIn 0.3s ease-in-out;
                max-width: 90%;

                .el-checkbox {
                    margin-top: 10px;
                }





                .avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    overflow: hidden;
                    flex-shrink: 0;

                    img {
                        width: 40px;
                        height: 40px;
                        object-fit: cover;
                    }
                }

                .msg-list-right {
                    position: relative;
                    padding-bottom: 28px;



                    .bubble {
                        position: relative;
                        border-radius: 8px;
                        word-break: break-word;
                        overflow: auto;
                        max-width: 100%;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;

                        &.LLM {
                            padding: 12px;
                        }
                    }
                }

                &.USER {
                    align-self: flex-end;
                    flex-direction: row-reverse;

                    .bubble {
                        background-color: #F6FCF9;
                        border-radius: 16px 0 16px 16px;


                    }
                }

                &.ASSISTANT {
                    align-self: flex-start;

                    .bubble {
                        background-color: #F9FAFB;
                        border-radius: 0 16px 16px 16px;
                    }
                }
            }
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}





.share-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    padding: 16px;
    z-index: 100;

    .share-bottom-content {
        width: 800px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 8px;
        box-sizing: border-box;
    }

    .share-buttons {
        display: flex;
        gap: 12px;
    }
}
</style>