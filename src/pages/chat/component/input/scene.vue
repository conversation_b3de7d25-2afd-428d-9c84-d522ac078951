<template>
    <div class="scene" v-if="sceneFeatureSwitches.sceneCode && sceneFeatureSwitches.sceneCode !== 'GENERAL_CHAT'">
        <div class="title">
            场景：{{ sceneFeatureSwitches.sceneName }}
        </div>
        <el-button class="out-scene" size="mini" @click="outScene">退出场景<i class="iconfont icon-tuichu1"></i></el-button>
    </div>
</template>


<script>

import { mapState } from 'vuex'
export default {
    computed: {
        ...mapState(['sceneFeatureSwitches'])
    },
    methods: {
        async outScene() {
            await this.$api.get(`/ai-main-app/api/chat/entryGeneralChat`, { params: { conversationId: this.$route.query.conversationId } })
            this.$emit('flashMsgList')
        }
    }
}
</script>


<style lang="less" scoped>
.scene {
    background: linear-gradient(94deg, #E0F2E9 1%, #7CD5A9 100%);
    border-radius: 12px 12px 0px 0px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
        font-size: 14px;
        font-weight: bold;
    }

    .out-scene {
        display: flex;
        align-items: center;

        .iconfont {
            margin-left: 5px;
            font-size: 14px;
        }
    }

}
</style>