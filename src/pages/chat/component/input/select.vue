<template>
    <el-dropdown @command="handleCommand" trigger="click" contenteditable="false" >
        <span class="custom-dropdown-link dataKey" :data-key="dataKey">
            <span class="value" v-if="currentLabel">{{ currentLabel }}</span>
            <span class="placeholder" v-if="!currentLabel">{{ placeholder }}</span>
            <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="input-select">
            <el-dropdown-item v-for="option in myOptions" :key="option.value" :command="option.value"
                :disabled="option.disabled" :divided="option.divided"
                :class="{ active: option.value === value }">
                {{ option.label }}
            </el-dropdown-item>
        </el-dropdown-menu>
    </el-dropdown>
</template>

<script>
export default {
    name: 'Select',
    props: {
        placeholder: {
            type: String,
            default: '请选择'
        },
        value: {
            type: [String, Number],
            default: ''
        },
        options: {
            type: [Array, String],
            default: () => []
        },
        dataKey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            myOptions: []
        }
    },
    created() {
        this.initOptions()
    },
    computed: {
        currentLabel() {
            const option = this.myOptions.find(opt => opt.value === this.value)
            return option ? option.label : ''
        }
    },
    methods: {
        handleCommand(value) {
            this.$emit('input', value)
            this.$emit('change', {
                key: this.dataKey,
                value: value
            })
        },
        async initOptions() {
            if (Array.isArray(this.options)) {
                this.myOptions = [...this.options]
                return
            }
            const res = await this.$api.get(this.options)
            this.myOptions = res
        }
    }
}
</script>

<style scoped>
.custom-dropdown-link {
    background-color: #e0f2e9;
    border-radius: 4px;
    padding: 0 8px;
    margin: 0 4px;
    display: inline-block;
    color: #00b55b;
    font-weight: 400;
    font-size: 14px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    .placeholder{
        color: #73d6b5;
    }
}

.custom-dropdown-link:hover {
    opacity: 0.8;
}

.el-icon--right {
    margin-left: 5px;
    font-size: 12px;
}

.input-select{
    max-height: 260px;
    overflow: auto;
}

/* 选中项的样式 */
.el-dropdown-menu__item.active {
    color: #00b55b;
    font-weight: bold;
}
</style>