<template>
    <div class="cp-text-temp-wrap" @mousedown="handleMouseDown">
        <span class="placeholder" v-show="isShowPlaceholder">{{ placeholder }}</span>
        <div class="cp-text-temp" ref="textBox" contenteditable="true" @keydown="handleKeyDown" @input="handlerInput">
            <span v-for="(it, i) in temp" :key="i" class="item-wrapper" :class="[it.type]">
                <span v-if="it.type === 'tag'" class="tag" :data-placeholder="it.placeholder">
                    <span class="tag-text">{{ it.value || ' ' }}</span>
                    <span class="tag-placeholder" contenteditable="false" v-if="!it.value">{{ it.placeholder }}</span>
                </span>
                <span v-if="it.type === 'text'" class="text">{{ it.value }}</span>
                <Select v-if="it.type === 'select'" v-model="values[it.key]" class="select" :data-key="it.key" @change="updateValue" :options="it.options" :placeholder="it.placeholder"></Select>
            </span>
            <span class="endEmpty">&#xFEFF;</span>
        </div>
    </div>
</template>

<script>
import { mapState } from 'vuex'
import rangy from 'rangy'
import Select from './input/select.vue'
export default {
    components: {
        Select
    },
    props: {
        temp: {},
        placeholder: {}
    },
    data() {
        return {
            isShowPlaceholder: !this.temp,
            values: {}
        }
    },
    mounted() {
        // 初始化rangy
        rangy.init();
        setTimeout(() => {
            this.updateValue();
            // 设置光标到末尾
            const tagText = this.$el.querySelector('.tag-text');
            if (tagText) {
                this.setCursorPosition(tagText, 'end');
            } else {
                this.setCursorPosition(this.$refs.textBox, 'end');
            }
        }, 100)
    },
    watch: {
        temp: {
            immediate: true,
            handler() { 
                this.values = this.getInitValue()
            }
        }
    },
    computed: {     
        ...mapState(['sceneFeatureSwitches', 'isCompleted']),
    },
    methods: {
        $clearValue() {
            this.$refs.textBox.innerHTML = ''
            this.isShowPlaceholder = true
            this.updateValue()
        },
        $getValue() {
            if (!this.$refs.textBox) { 
                return ''
            }
            let text = ''
            this.$refs.textBox.childNodes.forEach(item => {
                
                if (item.nodeType === 3) {
                    // 直接添加文本节点内容
                    text += item.textContent
                } else if (item.nodeType === 1) {
                     if (item.classList.contains('tag')) {
                        // 如果是tag类，只添加tag-text子节点的文本
                        const tagText = item.querySelector('.tag-text')
                        if (tagText) {
                            text += tagText.textContent
                        }
                    } else if (item.classList.contains('select')) {
                        // 如果是下拉选择器
                        const elSelect = item.querySelector('.dataKey')
                        if (elSelect) {
                            const key = elSelect.dataset.key
                            text += this.values[key]
                        }
                    } else { 
                        text += item.textContent
                    }
                }
            })
            const v = text.trim()
            if (v === '/n') {
                return ''
            }
            return v
        },
        getInitValue() {
            if (!this.temp) { 
                return {}
            }
            const values = {}
            this.temp.forEach(it => { 
                if (it.type === 'select') { 
                    values[it.key] = it.value
                }
            })
            return values
        },
        handlerInput(event) {
            if (event && event.data === '@' && this.sceneFeatureSwitches.enableSkillSwitch) {
                // 遍历 textBox 下的所有文本节点
                const walker = document.createTreeWalker(
                    this.$refs.textBox,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let node;
                while ((node = walker.nextNode()) !== null) {
                    if (node.textContent.includes('@')) {
                        node.textContent = node.textContent.replace('@', '');
                    }
                }
                // 将光标移动到cp-text-temp元素里面最后一个元素
                this.setCursorPosition(this.$refs.textBox, 'end');
                // 触发AT事件
                this.$emit('AT')
            }
        },
        updateValue() {
            const value = this.$getValue()
            this.isShowPlaceholder = !value
            this.$emit('change', value)
        },
        handleMouseDown(event) {
            if (this.isShowPlaceholder) {
                event.preventDefault();
                // 找到第一个item-wrapper元素
                const itemWrapper = this.$el.querySelector('.item-wrapper');
                if (itemWrapper) {
                    // 将焦点聚焦到item-wrapper
                    this.setCursorPosition(itemWrapper, 'start');
                } else {
                    // 如果没有item-wrapper，聚焦到textBox
                    this.$refs.textBox.focus();

                }
            } else {
                const clickedElement = event.target;
                if (clickedElement.classList.contains('tag') && clickedElement.querySelector('.tag-placeholder')) {
                    const tagText = clickedElement.querySelector('.tag-text');
                    if (tagText) {
                        setTimeout(() => {
                            this.setCursorPosition(tagText, 'start');
                        })
                    }
                }
            }
        },
        handleKeyDown(event) {
            setTimeout(() => {
                this.updateValue()
            }, 100)
            if (event.key === 'Shift') {
                event.preventDefault();
                return;
            }

            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                this.$emit('enter');
                return;
            }

            const tagText = this.isOnTag('tag-text');
            if (tagText) {
                this.tagTextDeal(event, tagText);
                return;
            }
        },
        tagTextDeal(event, tagText) {
            // 检查是否是特殊按键
            const specialKeys = [
                'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
                'Shift', 'Control', 'Alt', 'Meta',
                'CapsLock', 'Escape', 'PageUp', 'PageDown',
                'Home', 'End', 'Insert', 'Delete'
            ];

            if (specialKeys.includes(event.key)) {
                return;
            }

            // 处理 Tab 键
            if (event.key === 'Tab') {
                event.preventDefault();
                const allTags = this.$el.querySelectorAll('.tag-text');
                const currentIndex = Array.from(allTags).indexOf(tagText);
                const nextIndex = (currentIndex + 1) % allTags.length;
                this.setCursorPosition(allTags[nextIndex], 'end');
                return;
            }

            // 检查是否按下删除键(Backspace)或删除键(Delete)
            if (event.key === 'Backspace' || event.key === 'Delete') {
                // 获取标签文本内容（去除空白）
                const text = tagText.textContent.trim();

                // 如果文本长度为1，则阻止默认事件
                if (text.length === 1) {
                    event.preventDefault();

                    // 找到标签的父元素（整个tag元素）
                    const tagElement = this.findParentWithClass(tagText, 'tag');
                    if (tagElement) {
                        // 清空tag-text内容
                        tagText.innerHTML = '&nbsp;';
                        // 创建并添加placeholder元素
                        const placeholder = document.createElement('span');
                        placeholder.className = 'tag-placeholder';
                        placeholder.textContent = `${tagElement.dataset.placeholder}`;
                        placeholder.setAttribute('contenteditable', 'false');
                        tagElement.appendChild(placeholder);

                        // 将光标移到tagText开始位置
                        this.setCursorPosition(tagText, 'start');
                    }
                }
                if (text.length === 0) { // 移除整个tag 
                    const tagElement = this.findParentWithClass(tagText, 'tag');
                    tagElement.remove();
                }
            } else {
                // 移除tagText父元素下面的placeholder
                const placeholder = tagText.parentNode.querySelector('.tag-placeholder');
                if (placeholder) {
                    tagText.parentNode.removeChild(placeholder);
                }
            }
        },
        // 使用rangy设置光标位置
        setCursorPosition(element, position = 'start') {
            // 确保element存在
            if (!element) return;

            // 创建rangy选择
            const selection = rangy.getSelection();

            // 清除当前选择
            selection.removeAllRanges();

            // 创建新范围
            const range = rangy.createRange();

            if (position === 'start') {
                // 设置光标在元素开始位置
                if (element.childNodes.length > 0) {
                    range.setStart(element.childNodes[0], 1);
                } else {
                    range.setStart(element, 0);
                }
                range.collapse(true);
            } else if (position === 'end') {
                // 设置光标在元素内容末尾
                range.selectNodeContents(element);
                range.collapse(false);
            }

            // 应用选择范围
            selection.addRange(range);

            // 聚焦元素
            element.focus();

            // 滚动到视图
            element.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
        },
        // 查找具有特定类名的父元素
        findParentWithClass(element, className) {
            let currentElement = element;
            while (currentElement) {
                if (currentElement.classList && currentElement.classList.contains(className)) {
                    return currentElement;
                }
                currentElement = currentElement.parentNode;
            }
            return null;
        },

        // 判断当前选择位置是否在指定类名的元素内
        isOnTag(className) {
            try {
                const selection = rangy.getSelection();
                if (selection.rangeCount === 0) return false;

                const range = selection.getRangeAt(0);
                const startContainer = range.startContainer;

                // 如果是文本节点，获取其父元素
                const targetElement = startContainer.nodeType === 3
                    ? startContainer.parentNode
                    : startContainer;

                // 检查元素本身或其父元素是否包含指定类名
                if (targetElement && targetElement.classList && targetElement.classList.contains(className)) {
                    return targetElement;
                }

                return false;
            } catch (error) {
                console.error('判断选择位置时出错:', error);
                return false;
            }
        },
    },
    beforeDestroy() {
        this.$clearValue()
    }
}
</script>

<style lang="less">
.cp-text-temp-wrap {
    position: relative;

    &>.placeholder {
        position: absolute;
        color: #9EA4B2;
        font-size: 14px;
        line-height: 1.5;
    }

    .cp-text-temp {
        -webkit-user-modify: read-write-plaintext-only;
        white-space: pre-wrap;
        overflow-wrap: break-word;
        outline: none;
        border: none;
        font-size: 15px;
        line-height: 1.6;
        color: #333333;

        .item-wrapper {
            display: inline;
        }

        .none {
            display: none;
        }

        .tag {
            background-color: #E0F2E9;
            border-radius: 4px;
            padding-right: 4px;
            margin: 0 4px;
            display: inline-block;
            color: #00B55B;
            font-weight: normal;
            font-size: 14px;

            .tag-placeholder {
                border: none;
                background: transparent;
                outline: none;
                padding: 0;
                margin: 0;
                color: #73D6B5;
                user-select: none;
                pointer-events: none;
                font-size: 14px;
                line-height: 1.5;
            }
        }

        .text {
            color: #333333;
            font-size: 15px;
            line-height: 1.6;
        }
    }
}
</style>