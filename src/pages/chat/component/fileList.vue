<template>
    <div class="page-chat-file-list" v-if="list && list.length">
        <div class="file" v-for="(it, i) in list" :key="it.id || i" v-loading="it.loading"
            element-loading-spinner="el-loading-spinne" @click="handleFileClick(it)">
            <el-image class="icon-img" :src="getIcon(it)"  fit="cover"/>
            <div class="right">
                <div class="title">{{ it.fileName || it.fileUrl }}</div>
                <div class="desc">{{ getDesc(it) }}</div>
            </div>
            <i v-show="!it.loading && !readOnly" class="el-icon-close delete-btn" @click="deleteFile(i)"></i>
        </div>

        <el-drawer title="查看解析结果" :visible.sync="drawerVisible" direction="rtl" :size="drawerSize">
            <div class="page-chat-file-list-drawer" v-if="currentFile && drawerVisible" >
                <div class="file-origin">
                    <div class="left">
                        <el-image class="icon-img" :src="getIcon(currentFile)" fit="cover" />
                        <div class="right">
                            <div class="title" :title="currentFile.fileName || currentFile.fileUrl">{{ currentFile.fileName || currentFile.fileUrl }}</div>
                            <div class="desc">{{ getDesc(currentFile) }}</div>
                        </div>
                    </div>
                    <div class="right">
                        <el-button icon="el-icon-download" v-if="isLink(currentFile)" @click="downFile(currentFile.fileUrl)">下载原始文件</el-button>
                        <el-button icon="el-icon-link"  @click="downFile(currentFile.fileUrl)" v-else>查看原始文档</el-button>
                    </div>
                </div>
                <div class="shadow-host">
                    <div v-if="currentFile.textType === 'MARKDOWN'" v-html="renderMarkdown(currentFile.bigtext)"></div>
                    <cp-shadow class="" :content="currentFile.bigtext" />
                </div>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import { file2bloburl } from '@/utils/file';
import { auth } from '@/utils/feishu.js'
import markdown from '@/utils/markdown'

export default {
    props: {
        readOnly: {},
        list: {}
    },
    data() {
        return {
            drawerVisible: false,
            currentFile: null
        }
    },
    computed: {
        drawerSize() {
            const width = window.innerWidth;
            if (width < 800) {
                return '100vw';
            } else if (width * 0.5 < 800) {
                return '800px';
            } else {
                return '50%';
            }
        }
    },
    methods: {
        getFileExtension(filename) {
            // 处理没有文件名的情况
            if (!filename) return '';
            
            // 处理以点结尾的文件名
            if (filename.endsWith('.')) return '';
            
            // 获取最后一个点号的位置
            const lastDotIndex = filename.lastIndexOf('.');
            
            // 如果没有点号，返回空字符串
            if (lastDotIndex === -1) return '';
            
            // 返回最后一个点号后面的部分
            return filename.slice(lastDotIndex + 1).toLowerCase();
        },
        downFile(url) { 
            window.open(url)
        },
        isLink(file) {
            return  ['WORD','PPT','EXCEL','PDF','TXT'].includes(file.type)
        },
        renderMarkdown(text) {
            return markdown.render(text)
        },
        getIcon(it) {
            if (it.type === 'IMAGE') {
                return it.fileUrl
            }
            const map = {
                WEB: 'https://oss.syounggroup.com/static/file/zeus/file_logo/link.png',
                WORD: 'https://oss.syounggroup.com/static/file/zeus/file_logo/Word.png',
                PPT: 'https://oss.syounggroup.com/static/file/zeus/file_logo/ppt.png',
                EXCEL: 'https://oss.syounggroup.com/static/file/zeus/file_logo/Excel.png',
                PDF: 'https://oss.syounggroup.com/static/file/zeus/file_logo/pdf.png',
                TXT: 'https://oss.syounggroup.com/static/file/zeus/file_logo/%E6%96%87%E6%9C%AC%E6%96%87%E6%A1%A3.png',
                FEISHU_DOC: 'https://oss.syounggroup.com/static/file/zeus/file_logo/%E9%A3%9E%E4%B9%A6%E6%96%87%E6%A1%A3.png',
                FEISHU_EXCEL: 'https://oss.syounggroup.com/static/file/zeus/file_logo/%E9%A3%9E%E4%B9%A6%E8%A1%A8%E6%A0%BC.png',
                FEISHU_WIKI: 'https://oss.syounggroup.com/static/file/zeus/file_logo/%E9%A3%9E%E4%B9%A6%E5%A4%9A%E7%BB%B4%E8%A1%A8%E6%A0%BC.png',
            }
            return map[it.type] || map.TXT
        },
        deleteFile(index) {
            this.list.splice(index, 1);
        },
        getFileType(file) {
            const FILE_TYPES = {
                IMAGE: { name: "图片", extensions: ["jpg", "png", "jpeg", "webp"] },
                VIDEO: { name: "视频", extensions: ["mp4", "mov", "avi", "wmv", "flv"] },
                AUDIO: { name: "音频", extensions: ["mp3", "wma", "wav"] },
                WEB: { name: "网页内容", extensions: [] },
                FEISHU_DOC: { name: "飞书文档", extensions: [] },
                FEISHU_EXCEL: { name: "飞书表格", extensions: [] },
                FEISHU_WIKI: { name: "飞书知识库", extensions: [] },
                WORD: { name: "word", extensions: ["docx"] },
                PPT: { name: "ppt", extensions: ["pptx"] },
                EXCEL: { name: "excel", extensions: ["xls", "xlsx"] },
                PDF: { name: "pdf", extensions: ["pdf"] },
                TXT: { name: "记事本", extensions: ["txt"] },
                RAR: { name: "压缩包", extensions: ["rar", "zip"] }
            };

            // 如果是字符串（URL），检查是否是飞书链接
            if (typeof file === 'string') {
                if (file.startsWith('https://syounggroup.feishu.cn/docx/')) {
                    return 'FEISHU_DOC';
                }
                if (file.startsWith('https://syounggroup.feishu.cn/wiki')) {
                    return 'FEISHU_WIKI';
                }
                if (file.startsWith('https://syounggroup.feishu.cn/sheets/')) {
                    return 'FEISHU_EXCEL';
                }
                if (file.startsWith('https://') || file.startsWith('http://')) {
                    return 'WEB';
                }
                return null;
            }

            // 如果是文件对象，根据扩展名判断
            if (file instanceof File) {
                const extension = this.getFileExtension(file.name);

                for (const [type, info] of Object.entries(FILE_TYPES)) {
                    if (info.extensions.includes(extension)) {
                        return type;
                    }
                }
            }

            return null;
        },
        getDesc(item) {
            let desc = `${item.type}•${this.getSize(item.fileSize)}`
            if (item.wordCount) {
                desc += `•约${this.getWordCount(item.wordCount)}个字`
            }
            return desc
        },
        async getFeishuRoot(type) {
            if (type === 'FEISHU_DOC' || type === 'FEISHU_WIKI' || type === 'FEISHU_EXCEL') {
                const res = await this.$api.get('/ai-main-app/api/feishuToken/hasValidUserAccessToken')
                if (!res) {
                    const { code, redirectUri } = await auth()
                    await this.$api.post('/ai-main-app/api/feishuToken/grandCodeToUserAccessToken', { code, redirectUri })
                }
            }
        },
        async addFile(data) {
            if (Array.isArray(data)) {
                if (this.list.length + data.length > 10) {
                    this.$message.error('最多只能上传10个文件');
                    return;
                }
                await Promise.all(data.map(file => this.addFile(file)))
                return
            }
            if (this.list.length >= 10) {
                this.$message.error('最多只能上传10个文件');
                return;
            }

            const type = this.getFileType(data)

            if (typeof data === 'string') {
                const item = {
                    fileUrl: data,
                    type,
                    loading: true
                }
                this.list.push(item)
                try {
                    await this.getFeishuRoot(type)
                    const res = await this.$api.post('/ai-main-app/api/conversationAttachment/linkToAttachment', { link: data })
                    item.loading = false
                    Object.assign(item, res)
                } catch (error) {
                    this.deleteFile(this.list.indexOf(item))
                }
                return;
            }

            // 文件类型验证
            const allowedExtensions = ['doc', 'docx', 'pdf', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'txt'];
            const fileExtension = this.getFileExtension(data.name);

            if (!allowedExtensions.includes(fileExtension)) {
                this.$message.error('不支持的文件类型，仅支持Word、PDF、Excel、PPT和图片文件(jpg/jpeg/png)');
                return;
            }

            // 文件大小验证
            const isImage = ['jpg', 'jpeg', 'png'].includes(fileExtension);
            const maxSize = isImage ? 10 * 1024 * 1024 : 100 * 1024 * 1024; // 图片10MB，其他文件100MB

            if (data.size > maxSize) {
                const sizeLimit = isImage ? '10MB' : '100MB';
                this.$message.error(`文件大小不能超过${sizeLimit}`);
                return;
            }

            if (typeof data !== 'string') {
                const item = {
                    fileName: data.name,
                    type,
                    loading: true
                }
                if (type === 'IMAGE') {
                    item.fileUrl = file2bloburl(data)
                }
                this.list.push(item)
                try {
                    const link = await this.$uploadBigFile(data, {});
                    const res = await this.$api.post('/ai-main-app/api/conversationAttachment/linkToAttachment', { link: link, fileName: data.name, fileSize: data.size, fileExtension: this.getFileExtension(data.name) })
                    item.loading = false
                    Object.assign(item, res)
                } catch (error) {
                    this.deleteFile(this.list.indexOf(item))
                }
            }
        },
        getWordCount(numb) {
            if (!numb) return '0';
            if (numb < 100) return numb.toString();
            if (numb < 1000) return `${numb}字`;
            if (numb < 10000) return `${(numb / 1000).toFixed(1)}千`;
            return `${(numb / 10000).toFixed(1)}万`;
        },
        getSize(size) {
            if (!size) return '0 B';
            const units = ['B', 'KB', 'MB', 'GB'];
            let index = 0;
            while (size >= 1024 && index < units.length - 1) {
                size /= 1024;
                index++;
            }
            return `${size.toFixed(1)} ${units[index]}`;
        },
        handleFileClick(file) {
            if (file.type !== 'IMAGE') {
                this.currentFile = file;
                this.drawerVisible = true;
            } else {
                this.$preview (file.fileUrl)
             }
        }
    }
}
</script>


<style lang="less">
.page-chat-file-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-bottom: 8px;

    .file {
        background-color: #F5F5F3;
        border-radius: 16px;
        padding: 8px;
        display: flex;
        gap: 8px;
        position: relative;
        align-items: center;
        cursor: pointer;

        .icon-img {
            height: 30px;
            width: 30px;
            border-radius: 8px;
            cursor: pointer;
        }

        .right {
            display: flex;
            flex-direction: column;
            gap: 4;

            .title {
                font-size: 14px;
                max-width: 250px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .desc {
                font-size: 12px;
                color: #9EA4B2;
            }
        }

        .delete-btn {
            position: absolute;
            top: -4px;
            right: -4px;
            color: #fff;
            display: flex;
            width: 14px;
            height: 14px;
            line-height: 1;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 100px;
            background-color: #262626;
            font-size: 10px;
            cursor: pointer;

            &:hover {
                opacity: 0.9;
            }
        }
    }
}

.page-chat-file-list-drawer {
    padding: 12px;
    display: flex;
    flex-direction: column;
    height: 100%;
    gap:12px;

    .file-origin {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #F5F6F8;
        border-radius: 8px;
        padding: 12px;

        .left {
            display: flex;
            align-items: center;
            gap:4px;
            width:calc(100% - 124px);
            img {
                width: 44px;
                height: 44px;
            }

            .right {
                display: flex;
                flex-direction: column;
                gap: 4px;
                width:calc(100% - 48px);
                overflow: hidden;

                .title {
                    font-size: 16px;
                    font-weight: bold;
                    max-width: 100%;
                    white-space: nowrap; 
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .desc {
                    font-size: 12px;
                    color: #9EA4B2;
                }
            }


        }
    }

    .shadow-host{
        flex: 1;
        overflow: auto;
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        padding: 12px;
    }
}
</style>
