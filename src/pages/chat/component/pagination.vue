<template>
    <div class="pagination">
        <el-button size="mini" :disabled="pageNo <= 1" @click="prevPage">上一页</el-button>
        <span>{{ pageNo }} / {{ pageLen }}</span>
        <el-button size="mini" :disabled="pageNo >= pageLen" @click="nextPage">下一页</el-button>
    </div>
</template>


<script>
export default {
    props: {
        pageNo: {
            type: Number,
            default: 1
        },
        pageSize: {
            type: Number,
            default: 10
        },
        total: {
            type: Number,
            default: 0
        }
    },
    computed: {
        pageLen() {
            return Math.ceil(this.total / this.pageSize)
        }
    },
    methods: {
        prevPage() {
            if (this.pageNo > 1) {
                this.$emit('update:pageNo', this.pageNo - 1)
                this.$emit('change')
            }
        },
        nextPage() {
            if (this.pageNo < this.pageLen) {
                this.$emit('update:pageNo', this.pageNo + 1)
                this.$emit('change')
            }
        }
    }
}
</script>

<style lang="less" scoped>
.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-right: 12px;
    gap: 20px;
    padding: 0px 0;

    span {
        color: #606266;
        font-size: 14px;
    }
}
</style>