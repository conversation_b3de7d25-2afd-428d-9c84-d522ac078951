<template>
    <transition name="slide-fade">
        <div class="agent" v-click-outside="handleClickOutside" v-show="isShow">
            <div class="title">
                <div class="left">
                    <div class="text">选择技能@</div>
                    <el-input placeholder="模糊搜索" v-model="searchText" size="mini" @input="handlerSearchDebounce(true)"
                        ref="searchInput" >
                        <i class="el-icon-search" slot="append"></i>
                    </el-input>
                    <div class="tabs">
                        <template v-for="(it, i) in tabs">
                            <div v-if="i < moreNumb" class="tab" :key="i" :class="{ active: it.value === activeTab }"
                                @click="handlerTabClick(it)" tabindex="0">{{ it.label }}</div>
                        </template>
                        <el-dropdown v-if="tabs.length > moreNumb" @command="handlerTabClick">
                            <div class="tab more">
                                更多<i class="el-icon-arrow-down"></i>
                            </div>
                            <el-dropdown-menu slot="dropdown" class="agent-more-dropdown">
                                <el-dropdown-item v-for="(it, i) in tabs.slice(moreNumb)" :key="i" :command="it"
                                    :class="{ 'is-active': it.value === activeTab }">
                                    {{ it.label }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
                <div class="right">
                    <div class="btn" v-if="false"><i class="el-icon-plus"></i><span>自定义技能</span></div>
                    <i class="el-icon-close" @click="$emit('close')"></i>
                </div>
            </div>
            <div class="content">
                <div class="skill-list">
                    <div class="empty" v-if="!list.length">
                        <div class="iconfont icon--zwsj"></div>
                        <div class="empty-text">暂无数据</div>
                    </div>
                    <div v-else class="skill-item" ref="skill" v-for="(it,i) in list" :key="it.id" @click="jumpSkill(it)" :class="{ selected: selectedIndex === i, [`index-${i}`]:true}">
                        <div class="row1">
                            <div class="title">
                                <img v-if="it.iconUrl" :src="it.iconUrl" alt="">
                                <span>{{ it.showName }}</span>
                            </div>
                            <div class="tag">{{ it.showType }}</div>
                        </div>
                        <div class="desc">
                            {{ it.description }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </transition>
</template>

<script>
import utils from '@/utils/index.js'
import skill from '../../../mixins/skill'

// 自定义指令
const clickOutside = {
    bind(el, binding) {
        el.clickOutsideEvent = function (event) {
            if (!(el === event.target || el.contains(event.target))) {
                binding.value(event)
            }
        }
        document.addEventListener('click', el.clickOutsideEvent)
    },
    unbind(el) {
        document.removeEventListener('click', el.clickOutsideEvent)
    }
}

export default {
    mixins: [skill],
    directives: {
        'click-outside': clickOutside
    },
    props: {
        isShow: {}
    },
    data() {
        return {
            moreNumb: 7,
            searchText: '',
            activeTab: 'MY_POSITION',
            tabs: [],
            list: [],
            pageNo: 1,
            pageSize: 8,
            selectedIndex:0
        }
    },
    watch:{ 
        list(v,o) { 
            if (JSON.stringify(v) !== JSON.stringify(o)) { 
                this.selectedIndex = 0
            }
        },
        selectedIndex() {
            this.$nextTick(() => {
                const selectedElement = Array.from(this.$refs.skill).find(el => el.classList.contains(`index-${this.selectedIndex}`))
                if (selectedElement) {
                    selectedElement.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    })
                }
            })
        },
        isShow(val) {
            if (val) {
                document.addEventListener('keydown', this.handleKeyDown)
            } else {
                document.removeEventListener('keydown', this.handleKeyDown)
            }
        }
    },
    methods: {
        async jumpSkill(it) { 
            this.$emit('update:isShow',false)
            await this.jump(it,true)
        },
        addTemp(skill) {
            this.$emit('addTemp', skill)
        },
        focus() {
            setTimeout(() => {
                this.$refs.searchInput.focus()
            }, 500)
        },
        handleClickOutside() {
            if (this.isShow) {
                this.$emit('close')
            }
        },
        async handlerSearch(init) {
            if (this.searching) {
                return
            }
            this.searching = true
            if (init) {
                this.pageNo = 1
            }
            if (this.searchText) { 
                this.activeTab = 'ALL'
            }
            let data
            if (this.activeTab !== 'MY_POSITION') {
                const res = await this.$api.post(`/ai-main-app/api/conversationSkill/listPage`, {
                    data: {
                        sceneCategory: this.activeTab,
                        showNameLike: this.searchText
                    },
                    pageNo: this.pageNo,
                    pageSize: 1000,
                })
                data = res.list
            } else {
                const res = await this.$api.get(`/ai-main-app/api/conversationSkill/listMyPosition`, {
                    params: { showNameLike: this.searchText }
                })
                data = res
            }

            this.list = data
            this.searching = false
        },
        handlerSearchDebounce: utils.debounce(async function (...args) {
            this.handlerSearch(...args)
        }, 300),
        async getTabs() {
            const res = await this.$api.get(`/ai-main-app/api/conversationSkill/listMyCategory`)
            this.tabs = res
        },
        handlerTabClick(tab) {
            if (this.searching) {
                return
            }
            this.pageNo = 1
            this.activeTab = tab.value
            this.searchText = ''
            this.handlerSearch()
        },
        handlerEnter() {
            if (this.list.length) {
                this.jump(this.list[this.selectedIndex], true)
                this.$emit('update:isShow',false)
            }
        },
        handleKeyDown(e) {
            if (e.key === 'Enter') {
                this.handlerEnter()
            } else if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                e.preventDefault()
                const direction = e.key.replace('Arrow', '').toLowerCase()
                this.handleArrowKey(direction)
            }
        },
        handleArrowKey(direction) {
            if (!this.list.length) return
            
            switch(direction) {
                case 'left':
                    this.selectedIndex = Math.max(0, this.selectedIndex - 1)
                    break
                case 'right':
                    this.selectedIndex = Math.min(this.list.length - 1, this.selectedIndex + 1)
                    break
                case 'up':
                    this.selectedIndex = Math.max(0, this.selectedIndex - 3)
                    break
                case 'down':
                    this.selectedIndex = Math.min(this.list.length - 1, this.selectedIndex + 3)
                    break

                
            }
        }
    },
    created() {
        this.getTabs()
        this.handlerSearch()
    },
    beforeDestroy() {
        document.removeEventListener('keydown', this.handleKeyDown)
    }
}   
</script>


<style lang="less" scope>
.agent {
    width: 100%;
    position: absolute;
    bottom: calc(100% + 12px);
    left: 0;
    background-color: #fff;
    box-shadow: 0 0 1px rgba(0, 0, 0, .3), 0 4px 14px rgba(0, 0, 0, .1);
    border-radius: 12px;

    &>.title {
        height: 42px;
        background: linear-gradient(to right, #DBFADA, #C7E1F7);
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        display: flex;
        justify-content: space-between;
        padding: 0 12px;

        .left {
            display: flex;
            gap: 8px;
            align-items: center;

            .text {
                font-size: 14px;
                font-weight: 600;
                margin-right: 8px;
            }

            .el-input {
                border-radius: 100px;
                display: flex;
                border: 1px solid #DCDFE6;
                background-color: #fff;
                overflow: hidden;
                // width: 28px;
                transition: width 0.2s ease;


                & {
                    width: 100px;

                    .el-input-group__append {
                        border-left: 1px solid #E6E8EB;
                    }

                    .el-input__inner {
                        display: inline;
                    }

                }

                .el-input-group__append {
                    border-radius: 100px;
                    width: 28px;
                    height: 28px;
                    box-sizing: border-box;
                    padding: 0;
                    overflow: hidden;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: none;
                    cursor: pointer;
                }



                .el-input__inner {
                    flex: 1;
                    border: 0;
                    padding: 0 6px;
                    padding-left: 12px;
                    overflow: hidden;
                    box-sizing: border-box;
                    // display: none;
                }
            }


            .tabs {
                display: flex;
                gap: 8px;

                .tab {
                    font-size: 12px;
                    border: 1px solid #E6E8EB;
                    border-radius: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 12px;
                    height: 28px;
                    background-color: #fff;
                    box-sizing: border-box;
                    cursor: pointer;
                    color: #6E768C;

                    &.active {
                        color: #00B55B;
                        border-color: #00B55B;
                    }

                    &.more {
                        padding: 0 8px;

                        i {
                            margin-left: 4px;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        .right {
            display: flex;
            gap: 8px;
            align-items: center;

            .btn {
                background-color: #00B55B;
                color: #fff;
                display: flex;
                border-radius: 100px;
                padding: 0 12px;
                align-items: center;
                font-size: 12px;
                gap: 4px;
                height: 28px;
                cursor: pointer;

                i {
                    position: relative;
                    top: 1px;
                }
            }

            i.el-icon-close {
                color: #A6A6A6;
                cursor: pointer;
                font-size: 22px;
            }
        }
    }

    .content {
        border: 1px solid rgba(0, 0, 0, 0.12);
        border-top: none;
        border-bottom-left-radius: 12px;
        border-bottom-right-radius: 12px;

        @media screen and (max-height: 600px) {
            div.skill-list {
                height: 134px;
            }
        }

        .skill-list {
            height: 206px;
            overflow: auto;
            display: flex;
            flex-wrap: wrap;
            padding: 12px;
            gap: 12px;
            position: relative;
            align-content: flex-start;

            .empty {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 18px;
                color: #909399;
                height: 100%;

                .iconfont {
                    font-size: 48px;
                }

                .empty-text {
                    font-size: 16px;
                }
            }

            .skill-item {
                width: calc((100% - 24px) / 3) !important;
            }
        }

        .pagination {
            margin-top: 6px;
        }
    }
}

.agent-more-dropdown {
    .el-dropdown-menu__item {
        font-size: 12px !important;
        color: #6E768C;
        min-width: 50px;
        justify-content: center;

    }
}

// 添加过渡动画
.slide-fade-enter-active {
    transition: all 0.2s ease-out;
}

.slide-fade-leave-active {
    transition: all 0.2s ease-in;
}

.slide-fade-enter,
.slide-fade-leave-to {
    transform: translateY(16px) scale(0.96);
    opacity: 0.8;
}
</style>