<template>

    <el-select class="cp-select-model" :value="value" @input="$emit('input', $event)" placeholder="请选择"
        popper-class="cp-select-model-dropdown">
        <el-option :value="it.value" :label="it.label" v-for="(it, i) in options" :key="i">
            <div class="model-wrap">
                <div class="row1">
                    <img :src="it.logo" alt="">
                    <span>{{ it.title }}</span>
                </div>
                <div class="row2">
                    {{ it.desc }}
                </div>
                <div class="row3">
                    <span v-for="(jt, j) in it.list" :key="j">{{ jt }}</span>
                </div>
            </div>
        </el-option>
    </el-select>
</template>

<script>
export default {
    props: {
        value: {}
    },
    computed: {
        options() {
            return [
                {
                    logo: 'https://oss.syounggroup.com/static/file/zeus/%E5%A4%A7%E6%A8%A1%E5%9E%8Blogo/GPT_logo.png',
                    label: 'GPT',
                    title: 'GPT',
                    value: 'GPT',
                    desc: 'Chat-GPT同款大模型',
                    list: ['支持发送图片', '指令遵循能力强']
                },
                {
                    logo: 'https://oss.syounggroup.com/static/file/zeus/%E5%A4%A7%E6%A8%A1%E5%9E%8Blogo/Deeepseek_logo.png',
                    label: 'DeepSeek',
                    title: 'DeepSeek',
                    value: 'DEEPSEEK',
                    desc: '全球知名的国产推理大模型',
                    list: ['展示思考过程', '不支持发送图片', '擅长中文']
                },
                {
                    logo: 'https://oss.syounggroup.com/static/file/zeus/%E5%A4%A7%E6%A8%A1%E5%9E%8Blogo/doubao_logo.png',
                    label: '豆包',
                    title: '豆包',
                    value: 'DOUBAO',
                    desc: '豆包底层同款大模型',
                    list: ['多模态，支持图片文本', '中文支持良好', '图片解析能力强']
                }
            ]
        }
    }
}
</script>

<style lang="less">
.cp-select-model {}

.cp-select-model-dropdown {
    .el-select-dropdown__list {
        display: flex;
        flex-direction: row !important;
        gap: 12px !important;

        .el-select-dropdown__item {
            height: auto;
            border-radius: 8px;
            padding: 0;
        }

        .model-wrap {
            background-color: #F5F5F5;
            border-radius: 8px;
            padding: 12px;
            width: 160px;
            height: 160px;
            text-align: center;

            .row1 {
                display: inline-flex;
                align-items: center;
                font-size: 16px;
                font-weight: 600;
                gap: 12px;

                img {
                    height: 30px;
                }
            }

            .row2 {
                font-size: 12px;
                margin-top: 0px;
                margin-bottom: 12px;
            }

            .row3 {
                line-height: 16px;
                display: flex;
                flex-direction: column;
                gap: 4px;
                text-align: left;
                margin-left: 24px;
                font-size: 12px;

                span {
                    &::before {
                        content: "•";
                        margin-right: 4px;
                    }
                }
            }

        }
    }
}
</style>

<style></style>