<template>
    <el-popover placement="bottom" trigger="hover" :width="300" v-model="popoverVisible"
        popper-class="md-link-preview-popover">
        <div class="link-preview">
            <div class="link-header">
                <span class="link-url">{{ href }}</span>
                <el-button type="text" size="mini" class="copy-btn">
                    <i class="iconfont icon-fuzhitishici" title="复制" @click="copyLink"></i>
                </el-button>
            </div>
            <div class="preview-container" v-if="popoverVisible">
                <iframe :src="href" frameborder="0" class="preview-iframe"></iframe>
            </div>
        </div>
        <span slot="reference" class="md-link-show">
            <i class="el-icon-link"></i>
            <span class="vue-link render">{{ name }}</span>
        </span>
    </el-popover>
</template>


<script>

const cancheMap = {}
const pendingRequests = {}

export default {
    props: {
        href: {
            type: String,
            required: true
        },
        title: {
            type: String
        }
    },
    data() {
        return {
            name: this.title || this.href,
            popoverVisible: false
        }
    },
    computed: {
        hrefMap() {
            return {
                'syounggroup.feishu.cn/sheets': '飞书多维表格',
                'syounggroup.feishu.cn/wiki': '飞书wiki',
            }
        }
    },
    methods: {
        copyLink() {
            this.$myUtils.copyText(this.href)
            this.$message.success('链接复制成功！')
        },
        async flashInfo() {
            if (this.title) {
                return
            }

            // 检查是否匹配 hrefMap 中的规则
            for (const [key, value] of Object.entries(this.hrefMap)) {
                if (this.href.includes(key)) {
                    this.name = value
                    return
                }
            }

            // 检查缓存中是否存在
            if (!cancheMap[this.href]) {
                // 如果已经有相同的请求正在进行中，等待该请求完成
                if (pendingRequests[this.href]) {
                    await pendingRequests[this.href]
                    this.name = cancheMap[this.href].title || this.href
                    return
                }

                // 创建新的请求并保存到 pendingRequests
                pendingRequests[this.href] = this.$api.post('/ai-main-app/api/conversationAttachment/fetchWebLinkMetaInfo', null, { params: { link: this.href } })
                    .then(res => {
                        cancheMap[this.href] = res
                        return res
                    })
                    .finally(() => {
                        // 请求完成后删除 pendingRequests 中的记录
                        delete pendingRequests[this.href]
                    })

                const res = await pendingRequests[this.href]
                this.name = res.title || this.href
            } else {
                this.name = cancheMap[this.href].title || this.href
            }
        }
    },
    mounted() {
        this.flashInfo()
    }
}
</script>

<style lang="less" scoped>
@border-color: #ebeef5;
@text-color: #606266;
@shadow-color: rgba(0, 0, 0, 0.1);

.md-link-show {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    max-width: 100%;
    vertical-align: bottom;
}

.link-preview {
    padding: 0px 6px 6px;
    background: #fff;
    border-radius: 8px;



    .link-header {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        padding-bottom: 0px;
        border-bottom: 1px solid #f0f0f0;

        .link-url {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 8px;
            color: @text-color;
            font-size: 13px;
        }

        .copy-btn {
            flex-shrink: 0;
        }
    }

    .preview-container {
        width: 100%;
        height: 180px;
        border: 1px solid @border-color;
        border-radius: 4px;
        overflow: hidden;
        background: #fff;
        position: relative;

        .preview-iframe {
            pointer-events: none;
            width: 500%;
            height: 500%;
            transform: scale(0.2);
            transform-origin: 0 0;
            position: absolute;
            left: 0;
            top: 0;
        }
    }
}
</style>