import { createApp, h } from 'vue';
import VueLink from './VueLink.vue'; // 确保路径正确
import Vue from 'vue';

const dataMap = {
}

export default function customLinkPlugin(md, options = {}) {
    // 保存原始的链接渲染规则
    const defaultLinkOpen = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options);
    };

    const defaultLinkClose = md.renderer.rules.link_close || function (tokens, idx, options, env, self) {
        return self.renderToken(tokens, idx, options);
    };

    // 覆盖链接渲染规则
    md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
        const token = tokens[idx];
        const href = getAttr(token, 'href');
        const text = tokens[idx + 1]?.content || '';

        const isExternal = /^(https?:)?\/\//.test(href);
        const randomNum = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
        const linkId = `vue-link-${Date.now()}-${randomNum}-${idx}`;

        // 自定义组件
        if (isExternal) {
            dataMap[linkId] = {
                href,
                title: href === text ? '' : text
            }
            pushAttr(token, 'class', 'md-link')
            pushAttr(token, 'data-link-id', linkId)
        }

        return defaultLinkOpen(tokens, idx, options, env, self);
    };

    md.renderer.rules.link_close = function (tokens, idx, options, env, self) {
        // 使用默认渲染逻辑
        return defaultLinkClose(tokens, idx, options, env, self);
    };
}


function getAttr(token, key) {
    return token.attrGet ? token.attrGet(key) : token.attrs?.find(attr => attr[0] === key)?.[1] || '';
}


function pushAttr(token, name, value) {
    // 检查 token 是否有 attrPush 方法
    if (typeof token.attrPush === 'function') {
        token.attrPush([name, value]);
    } else {
        // 如果没有 attrPush 方法，手动操作 attrs 数组
        // 确保 attrs 数组存在
        token.attrs = token.attrs || [];

        // 检查属性是否已存在，如果存在则更新，否则添加
        const index = token.attrs.findIndex(attr => attr[0] === name);
        if (index !== -1) {
            token.attrs[index][1] = value; // 更新现有属性
        } else {
            token.attrs.push([name, value]); // 添加新属性
        }
    }
}


function getDoms(el) {
    const doms = el.querySelectorAll('.md-link')
    const ids = []
    doms.forEach(dom => {
        ids.push(dom.dataset.linkId)
    })
    return {
        doms,
        ids
    }
}


export function renderCmp() {
    const { doms, ids } = getDoms(this.$el)
    // 存储创建的 Vue 实例
    this.vueInstances = this.vueInstances || new Map();
    const VueLinkCtor = Vue.extend(VueLink);
    ids.forEach((id, index) => {
        const dom = doms[index];
        const linkData = dataMap[id];
        if (linkData && !this.vueInstances.has(id)) {
            // 创建组件实例
            const instance = new VueLinkCtor({
                propsData: {
                    ...linkData
                }
            });
            // 挂载到内存节点
            instance.$mount();
            // 清空原有内容并插入组件 DOM
            dom.innerHTML = '';
            dom.appendChild(instance.$el);
            // 存储实例
            this.vueInstances.set(id, instance);
        }
    });
}


export function destoryCmp() {
    if (this.vueInstances) {
        this.vueInstances.forEach((instance, id) => {
            instance.$destroy();
            if (instance.$el && instance.$el.parentNode) {
                instance.$el.parentNode.removeChild(instance.$el);
            }
            delete dataMap[id];
        });
        this.vueInstances.clear();
    }
}