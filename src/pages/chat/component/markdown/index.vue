<template>
    <markdown-it-vue ref="myMarkdownItVue" :content="myContent" :options="options" @render-complete="renderComplete" />
</template>

<script>
import MarkdownItVue from 'markdown-it-vue'
import 'markdown-it-vue/dist/markdown-it-vue.css'
import customLinkPlugin, { renderCmp, destoryCmp } from './custom-link-plugin.js'
import { mapState } from 'vuex'

export default {
    components: {
        MarkdownItVue
    },
    props: {
        content: {},
        isUser: {}
    },
    data() {
        return {
        }
    },
    computed: {
        myContent() {
            if (!this.content) return '';
            
            // 匹配 http 或 https 链接的正则表达式，排除所有标点符号
            const urlRegex = /(https?:\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&()*+=%]+)/g;
            
            // 在链接前后添加空格
            return this.content.replace(urlRegex, ' $1 ');
        },
        options() {
            return {
                markdownIt: {
                    linkify: true,
                    html: true,
                    breaks: true
                },
                linkAttributes: {
                    attrs: {
                        target: '_blank',
                        rel: 'noopener',
                    }
                },
                katex: {
                    throwOnError: false,
                    errorColor: '#cc0000'
                },
                icons: 'font-awesome',
                githubToc: {
                    tocFirstLevel: 2,
                    tocLastLevel: 3,
                    tocClassName: 'toc',
                    anchorLinkSymbol: '',
                    anchorLinkSpace: false,
                    // anchorClassName: 'anchor',
                    // anchorLinkSymbolClassName: 'octicon octicon-link'
                },
                mermaid: {
                    theme: 'default'
                },
                image: {
                    hAlign: 'left',
                    viewer: true
                },
                echarts: {
                    width: '100%',
                    height: 400
                }
            }
        }
    },
    watch: {
        content: {
            handler(newVal, oldVal) {
                if (this.isUser) { 
                    return 
                }
                // 清除之前的定时器
                if (this.contentTimer) {
                    clearTimeout(this.contentTimer);
                } 
                // 设置3秒定时器
                this.contentTimer = setTimeout(() => {
                    this.renderCmp();
                }, 3000);
            },
            immediate: true
        }
    },
    methods: {
        renderComplete() {
            if (this.isUser) {
                this.renderCmp()
            }
        },
        renderCmp: renderCmp,
        destoryCmp: destoryCmp
    },
    beforeMount() {
        this.destoryCmp()
    },
    mounted() {
        this.$refs.myMarkdownItVue.use(customLinkPlugin)
        if (this.isUser) {
            const md = this.$refs.myMarkdownItVue.md
            md.disable(['heading','list']);
        }
    }
}
</script>

<style lang="less">
html .markdown-body {
    font-size: 14px;
    line-height: 24px;
    background: transparent !important;
    max-width: calc(100vw - 24px);
    color: rgba(0, 0, 0, 0.85);

    // 标题样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        font-weight: bold;
        margin-top: 8px;
        margin-bottom: 8px;
        border-bottom: none;
    }

    h1 {
        font-size: 18px;
    }

    h2 {
        font-size: 16px;
    }

    h3,
    h4,
    h5,
    h6 {
        font-size: 14px;
    }

    // 段落和文本样式
    // &>p {
    //     margin: 12px 0;
    // }

    a {
        color: #3662EC;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    // 代码样式


    pre {
        background-color: #2d2d2d;
        color: #ccc;
        border-radius: 4px;

        code {
            background-color: rgba(0, 0, 0, 0.04);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            white-space: pre-wrap;
            /* 保留空格和换行符，但允许长文本换行 */
            word-break: break-all;
            /* 允许在任意字符处换行 */
            overflow-wrap: anywhere;
            /* 现代浏览器替代方案，允许在任意位置换行 */
        }
    }

    .markdown-it-vue-alert-icon {
        top: 10.5px10.5px;
    }

    // 图片样式
    img {
        max-width: 100%;
        max-height: 400px;
        border-radius: 4px;
        cursor: pointer;
    }

    p {
        margin-bottom: 8px;
    }

    ul {
        margin-bottom: 8px;
    }

    // 分割线样式
    hr {
        height: 1px;
        border: 0;
        background-color: #E6E8EB;
        margin: 8px 0;
    }


    // 图表和流程图样式
    .mermaid,
    .md-flowchart,
    .md-echarts {
        overflow: auto;
    }

    .stretchy.mtight {
        max-width: 700px;
        overflow: hidden;
    }

    &>* {
        max-width: 700px;
    }
}
</style>