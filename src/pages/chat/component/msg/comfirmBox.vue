<template>
    <div class="msg-comfirm-box" :class="[msg.role]">
        <div class="title">
            <markdown class="content" :content="content.body.content"></markdown>
        </div>
        <div class="btns">
            <el-button v-for="(it,i) in content.body.buttons" :key="i" :type="it.type" :disabled="it.disabled" size="mini" @click="btnFnCall(it)">{{ it.name }}</el-button>
        </div>
    </div>
</template>

<script>
import markdown from '../markdown/index.vue'
import btnFnCall from './mixins/btnFnCall'
export default {
    mixins: [btnFnCall],
    components: {
        markdown
    },
    props: {
        msg: {}
    },
    computed: {
        content() { 
            return JSON.parse(this.msg.content)
        }
    },
    methods: {
    }
}
</script>


<style lang="less" scoped>
.msg-comfirm-box {
    display: flex;
    flex-direction: column;
    gap:12px;
    padding: 12px;
    .title{
        font-size: 14px;
    }
    .btns{
        display: flex;
        justify-content: flex-end;
    }
}
</style>