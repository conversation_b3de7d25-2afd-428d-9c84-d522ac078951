<template>
    <div class="msg-thinking">
        <div class="thinking-dots">
            <span class="dot dot-1"></span>
            <span class="dot dot-2"></span>
            <span class="dot dot-3"></span>
        </div>
    </div>
</template>


<script>
export default {
    props: { 
    },
    methods: {
    }
}

</script>



<style lang="less" scoped>
.msg-thinking {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 0;
    
    .thinking-dots {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: linear-gradient(135deg, #32C079, #28A06A);
            animation: thinking-bounce 1.4s infinite ease-in-out both;
            position: relative;
        }
        
        .dot-1 {
            animation-delay: -0.32s;
            
            &::before {
                animation-delay: -0.32s;
            }
        }
        
        .dot-2 {
            animation-delay: -0.16s;
            
            &::before {
                animation-delay: -0.16s;
            }
        }
        
        .dot-3 {
            animation-delay: 0s;
            
            &::before {
                animation-delay: 0s;
            }
        }
    }
}

@keyframes thinking-bounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

@keyframes thinking-pulse {
    0%, 80%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.3;
    }
    40% {
        transform: translate(-50%, -50%) scale(1.4);
        opacity: 0.8;
    }
}

// 深色主题适配
@media (prefers-color-scheme: dark) {
    .msg-thinking {
        .thinking-dots {
            .dot {
                background: linear-gradient(135deg, #32C079, #2DB573);
                
                &::before {
                    background: rgba(50, 192, 121, 0.25);
                }
            }
        }
    }
}

// 悬停效果
.msg-thinking:hover {
    .thinking-dots {
        .dot {
            animation-duration: 1.2s;
        }
    }
}
</style>