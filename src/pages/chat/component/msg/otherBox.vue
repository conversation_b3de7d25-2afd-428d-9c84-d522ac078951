<template>
    <div class="msg-ther-box">
        <render-openinfo :msg="msg" v-if="msg.type ==='OPENING_INFO'" @updateMsg="updateMsg"></render-openinfo>
        <render-comfirm-box :msg="msg" v-if="msg.type ==='CONFIRM_BOX'" @updateMsg="updateMsg"></render-comfirm-box>
    </div>
</template>


<script>
import renderComfirmBox from './comfirmBox.vue'
import renderOpeninfo from './openinfo.vue'
export default {
    components: {
        renderComfirmBox,
        renderOpeninfo
    },
    props: {
        msg: {}
    },
    methods: {
        updateMsg() { 
            this.$emit('updateMsg')
        }
    }
}
</script>