<template>
    <div class="msg-reason-content-wrapper" v-if="msg.reasonContent">
        <div class="reason-header" @click="toggleReason(msg)">
            <span class="reason-title">{{ msg.isReasonExpanded ? '隐藏思考' : '显示思考' }}</span>
            <i :class="{ 'el-icon-arrow-up': msg.isReasonExpanded, 'el-icon-arrow-down': !msg.isReasonExpanded }"></i>
        </div>
        <markdown v-if="msg.reasonContent" class="reason-content" :class="{ show: msg.isReasonExpanded }"
            :content="msg.reasonContent"></markdown>
    </div>
</template>


<script>
import markdown from '../markdown/index.vue'

export default {
    components: {
        markdown
    },
    props: {
        msg: {}
    },
    methods: {
        toggleReason(msg) {
            this.$set(msg, 'isReasonExpanded', !msg.isReasonExpanded);
        },
    }
}

</script>



<style lang="less" scoped>
.msg-reason-content-wrapper {
    border-left: 2px solid #E6E8EB;
    padding-left: 12px;

    .reason-header {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 0;
        color: #6E768C;
        font-size: 12px;

        .reason-title {
            font-weight: 500;
        }

        i {
            font-size: 12px;
            transition: transform 0.3s ease;
        }
    }

    .reason-content {
        transition: all 0.3s ease;
        overflow: hidden;
        max-height: 0;
        opacity: 0;

        /deep/ .markdown-body {
            color: #9EA4B2;
            font-size: 12px;
        }

        &.show {
            margin-top: 8px;
            max-height: unset;
            opacity: 1;
        }
    }
}
</style>