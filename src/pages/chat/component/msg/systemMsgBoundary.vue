<template>
    <div class="msg-system-boundary">
        <span class="line"></span>
        <span class="text">{{ text }}</span>
        <span class="line"></span>
    </div>
</template>


<script>
export default {
    props: {
        msg: {}
    },
    computed: {
        text() { 
            return this.msg.content || '已清除上下文'
        }
    }
}
</script>


<style lang="less" scoped>
.msg-system-boundary {
    display: flex;
    align-items: center;
    justify-content: center;
    gap:24px;
    width: 800px;
    .line {
        flex: 1;
        height: 1px;
        background: #E6E8EB;
    }
    .text {
        color: #9EA4B2;
        font-size: 12px;
        white-space: nowrap;
    }
}

</style>