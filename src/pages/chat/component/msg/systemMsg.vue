<template>
    <div class="msg-system-box">
        <systemMsgBoundary v-if="msg.type === 'LLM_BOUNDARY'" :msg="msg"/>
        <systemMsgInfo v-if="msg.type === 'SYSTEM_INFO'" :msg="msg"/>
    </div>
</template>

<script>
import systemMsgBoundary  from './systemMsgBoundary.vue';
import systemMsgInfo  from './systemMsgInfo.vue';
export default {
    components: {
        systemMsgBoundary,
        systemMsgInfo
    },
    props: {
        msg: {}
    }
}
</script>

<style lang="less" scoped>
 
</style>