export default {
    methods: {
        async btnFnCall(btn) {
            if (btn.callbackType === 'URL') {
                await this.$api.post(btn.callbackValue, btn.callbackBody && { ...JSON.parse(btn.callbackBody) }, { params: { conversationId: this.$route.query.conversationId } })
            }
            if (btn.callbackType === 'AUTO_SUBMIT_Text') {
                this.$emit('sendMsg', { content: btn.text })
            }
            this.$emit('updateMsg')
        }
    }
}