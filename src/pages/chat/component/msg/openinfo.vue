<template>
    <div class="msg-openinfo">
        <div class="title">{{ content.header.title }}</div>
        <markdown class="content" :content="content.body.content"></markdown>
    </div>
</template>

<script>
import markdown from '../markdown/index.vue'
export default {
    components: {
        markdown
    },
    props: {
        msg: {}
    },
    computed: {
        content() {
            return JSON.parse(this.msg.content)
        }
    },
    methods: {
    }
}
</script>


<style lang="less" scoped>
.msg-openinfo {
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    overflow: hidden;

    .title {
        font-size: 14px;
        font-weight: 600;
        padding: 8px;
        background: linear-gradient(94deg, #e0f2e9 1%, #7cd5a9);
        text-align: center;
    }

    .content {
        padding: 12px;
        background: #F9FAFB;

        /deep/ .markdown-body {
            color: #4A5568;
            font-size: 12px;
        }
    }

}
</style>