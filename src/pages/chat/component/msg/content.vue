<template>
    <div class="msg-content-wrap" :class="[msg.role]" v-if="msg.content">
        <markdown class="content" :content="msg.content" :isUser="msg.role === 'USER'"></markdown>
    </div>
</template>

<script>
import markdown from '../markdown/index.vue'

export default {
    components: {
        markdown
    },
    props: {
        msg: {}
    },
    methods: {
        renderMarkdown(content) {
            return markdown.render(content);
        },
    }
}
</script>


<style lang="less" scoped>
.msg-content-wrap{
    .content{
        word-break: break-word;
    }
}
</style>