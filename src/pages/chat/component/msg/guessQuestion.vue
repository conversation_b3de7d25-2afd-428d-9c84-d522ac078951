<template>
    <div class="msg-guess-question">
        <template v-if="!loading && list.length">
            <div class="title" v-if="!loading">猜你想问：</div>
            <div class="list" v-if="!loading">
                <el-button 
                    size="mini" 
                    type="primary" 
                    plain
                    round
                    v-for="(it, i) in list" 
                    :key="i" 
                    @click="btnFnCall(it)"
                    class="list-item"
                >
                    {{ it.text }}
                </el-button>
            </div>
        </template>
        <think v-if="loading"/>
    </div>
</template>


<script>
import { mapState } from 'vuex'
import utils from '@/utils/index.js'
import btnFnCall from './mixins/btnFnCall'
import think from './think.vue'

export default {
    components: {
        think
    },
    mixins:[btnFnCall],
    props: {
        msgList: {}
    },
    data() { 
        return {
            list: [],
            loading: false
        }
    },
    watch: {},
    computed: {
        ...mapState(['sceneFeatureSwitches']),
        conversationId() {
            return this.$route.query.conversationId
        }
    },
    methods: {
        async getGuess  () {
            if (!this.conversationId) {
                return
            }
            
            this.loading = true
            
            try {
                const res = await this.$api.get('/ai-main-app/api/chat/suggestedQuestions', { params: { conversationId: this.conversationId } })
                this.list = res.body.texts || []
                this.$emit('scrollToBottom')
            } catch (error) {
                console.error('获取推荐问题失败:', error)
                this.list = []
            } finally {
                this.loading = false
            }
        }
    },
    mounted() { 
        this.getGuess()
    }
}
</script>

<style lang="less">
.msg-guess-question {
    padding-left: 66px;
    margin-top: 0px;
    
    .title {
        font-size: 14px;
        color: #666;
        margin-bottom: 12px;
        font-weight: 500;
    }
    
    .list {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 8px;
        align-items: flex-start;
        
        .list-item {
            max-width: calc(100% - 120px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-left: 0;
        }
    }
    
    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        
        .loading-text {
            font-size: 13px;
            color: #999;
            margin-top: 8px;
        }
    }
    
    .empty-state {
        padding: 20px 0;
        text-align: center;
        
        .empty-text {
            font-size: 13px;
            color: #999;
        }
    }
}
</style>