<template>
    <div class="msg-opt-list" :class="{ 'is-last': isLast }" v-if="isCompleted || !isLast">
        <el-tooltip class="item" effect="dark" content="清空上下文" placement="top" v-if="isLast && sceneFeatureSwitches.allowClearContext">
            <i class="iconfont icon-qingchushangxiawen-toumingdi" @click="clearMsg"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="复制" placement="top">
            <i class="iconfont icon-fuzhitishici" @click="copyText(msg.content)"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="分享" placement="top" v-if="sceneFeatureSwitches.allowSharing">
            <i class="iconfont icon-fenxiang1" @click.stop="shareMsg(msg)"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="重新生成" placement="top" v-if="sceneFeatureSwitches.allowReGenerate">
            <i class="iconfont icon-shuaxin" @click="reBuild(msg)"></i>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" content="删除" placement="top" v-if="sceneFeatureSwitches.allowDelete">
            <i class="iconfont icon-shanchu" @click="delMsg(msg)"></i>
        </el-tooltip>
    </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
    props: {
        isLast: {},
        msg: {},
        list: {}
    },
    computed: {
        ...mapState(['sceneFeatureSwitches','isCompleted'])
    },
    methods: {
        async delMsg(msg) {
            try {
                await this.$confirm('删除后，聊天记录不可恢复，对话内的文件也将被彻底删除。', '是否删除该条消息？', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                await this.$api.post('/ai-main-app/api/conversationMessage/deleteById', { id: msg.id });
                const index = this.list.findIndex(item => item.id === msg.id);
                if (index !== -1) {
                    this.list.splice(index, 1);
                }
                this.$message.success('删除成功');
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error('删除失败');
                }
            }
        },
        copyText(text) {
            this.$myUtils.copyText(text)
            this.$message.success('复制成功!')
        },
        shareMsg(msg) {
            this.$emit('shareMsg', msg)
        },
        async reBuild(msg) {
            msg.isReasonExpanded = true
            this.$emit('reBuild', msg)
        },
        async clearMsg() { 
            await this.$api.get('/ai-main-app/api/chat/clearLLMContext', { params: { conversationId: this.$route.query.conversationId } })
            this.$emit('updateMsg')
        }
    }
}
</script>

<style lang="less">
.cp-msg-list-wrap{
    .msg-list-right{
        &:hover{
            .msg-opt-list{
                display: flex;
            }
        }
    }
}

</style>

<style lang="less" scoped>
.msg-opt-list {
    display: none;
    gap: 8px;
    position: absolute;
    bottom: 0px;
    left: 12px;
    color: rgba(0, 0, 0, .5);

    .iconfont {
        cursor: pointer;

        &.icon-shuaxin {
            display: none;
        }
    }

    &.is-last {
        display: flex;

        .iconfont {
            &.icon-shuaxin {
                display: inline;
            }
        }
    }
}
</style>