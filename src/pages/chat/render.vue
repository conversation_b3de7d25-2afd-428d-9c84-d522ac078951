<template>
    <div class="page-chat" :class="{ 'is-new-chat': isNewChat, 'open-share': openShare }">
        <msg-list ref="msgList" @ssePending="ssePending" @retryMsg="retryMsg" @sendMsg="sendMsg"
            :openShare.sync="openShare"></msg-list>
        <div class="home-show">
            <div class="user-info-wrap" v-show="userInfo.name">
                <img v-if="userInfo.gender === '1'" src="@/assets/man.png" alt="">
                <img v-else src="@/assets/woman.png" alt="">
                <div class="box">
                    <span class="username">{{ userInfo.name }}</span>
                    <span class="position">{{ userInfo.position }}</span>
                </div>
            </div>
            <div class="right">
                <div class="text">
                    <div class="row1">{{ greeting }}</div>
                    <div class="row2">我是ZEUS</div>
                    <div class="row3">岗位专属推荐</div>
                    <div class="skill-list">
                        <div class="skill-item" v-for="(it, i) in agentList" :key="i" @click="jump(it, true)">
                            <div class="row1">
                                <div class="title">
                                    <img v-if="it.iconUrl" :src="it.iconUrl" alt="">
                                    <span>{{ it.showName }}</span>
                                </div>
                                <div class="tag">{{ it.showType }}</div>
                            </div>
                            <div class="desc">
                                {{ it.description }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <sendInput :inputPlaceholder="inputPlaceholder" ref="sendInput" v-model="inputValue" @sendMsg="handleSendMsg"
            @startReceiveMsg="startReceiveMsg" @flashMsgList="flashMsgList" />
        <div v-if="!$route.query.conversationId" class="chat-desc">
            使用时请遵守法律法规及公司《信息安全管理制度》。请勿向ZEUS发送公司敏感信息及机密数据，如薪酬/成本/未公开财务数据等。AI生成内容仅供参考，请仔细甄别。</div>
    </div>
</template>

<script>
import sendInput from './component/sendInput.vue'
import msgList from './component/msgList.vue'
import skill from '@/mixins/skill'
import { mapState } from 'vuex'

export default {
    mixins: [skill],
    props: {
        inputPlaceholder: {
            default: '发消息、输入@选择技能、粘贴网页链接/飞书文档/图片/文件作为附件'
        }
    },
    components: {
        sendInput,
        msgList
    },
    data() {
        return {
            title: '新对话',
            inputValue: {
                temp: null,
                files: []
            },
            agentList: [],
            openShare: false
        }
    },
    watch: {
        '$route.query.skill': {
            async handler(newVal, oldValue) {
                if (!oldValue && newVal) {
                    const res = await this.$api.get(`/ai-main-app/api/conversationSkill/get`, { params: { id: newVal } })
                    setTimeout(() => {
                        this.addTemp(res)
                    }, 100)

                    // 清空URL上的skill参数
                    const query = { ...this.$route.query }
                    delete query.skill
                    this.$router.replace({ query })
                }
            },
            immediate: true
        }
    },
    computed: {
        ...mapState(['sceneFeatureSwitches']),
        isNewChat() {
            return !this.$route.query.conversationId
        },
        greeting() {
            const hour = new Date().getHours()
            if (hour >= 5 && hour < 12) {
                return '早上好！'
            } else if (hour >= 12 && hour < 14) {
                return '中午好！'
            } else if (hour >= 14 && hour < 18) {
                return '下午好！'
            } else {
                return '晚上好！'
            }
        },
        userInfo() {
            return this.$store.state.userInfo
        },
    },
    methods: {  
        sendMsg(msgContent) {
            this.$refs.sendInput.$sendMsg(msgContent)
        },
        $goAgent(skill) {
            this.jump(skill, true)
        },
        flashMsgList() {
            this.$refs.msgList.initListData()
        },
        startReceiveMsg() {
            this.$refs.msgList.scrollToBottom(true, 'instant')
        },
        ssePending(id) {
            this.$refs.sendInput.ssePending(id)
        },
        retryMsg(msg) {
            this.$refs.sendInput.retryMsg(msg)
        },
        handleSendMsg(msg) {
            this.$refs.msgList.$addMsg(msg)
        },

        async getMyAgent() {
            const res = await this.$api.get(`/ai-main-app/api/conversationSkill/listMyPosition`)
            this.agentList = res.slice(0, 3)
        },
        addTemp(item) {
            this.$refs.sendInput.addTemp(item)
        }
    },
    created() {
        this.getMyAgent()
    }
}
</script>

<style lang="less">
#app .page-chat {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    position: relative;

    &.open-share {
        .cp-send-input {
            display: none;
        }
    }

    .home-show {
        display: none;
    }

    .chat-desc {
        position: absolute;
        width: 100%;
        max-width: calc(100vw - 24px);
        text-align: center;
        color: #9ea4b2;
        font-size: 12px;
        bottom: -20px;
        margin: auto;
        min-width: 800px;
        bottom: 34px;
    }

    &.is-new-chat {
        justify-content: center;

        .cp-msg-list-wrap {
            display: none;
        }

        .home-show {
            display: flex;
            gap: 12px;
            width: 700px;
            margin: -46px auto 12px;


            .user-info-wrap {
                display: flex;
                flex-direction: column;
                align-items: center;

                img {
                    height: 100px;
                }

                .box {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                    align-items: center;
                    justify-content: center;
                    border-radius: 8px;
                    background: linear-gradient(127deg, #E0F2E9 0%, #7CD5A9 100%);
                    width: 100px;
                    height: 100px;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);

                    .username {
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .position {
                        font-size: 12px;
                        color: #303133;
                    }
                }
            }

            .right {
                .text {

                    &>.row1,
                    .row2 {
                        color: #00B55B;
                        font-size: 22px;
                        font-weight: bold;
                        margin-top: 12px;
                        line-height: 1em;
                    }


                    &>.row1 {
                        margin-top: 18px;
                    }

                    .row3 {
                        color: #32C079;
                        font-size: 12px;
                        margin-top: 22px;

                    }

                    .skill-list {
                        display: flex;
                        gap: 12px;
                        margin-top: 4px;


                    }
                }
            }

        }
    }


    .cp-send-input {
        width: 800px;
        box-sizing: border-box;
        margin: 0px auto 24px;
    }


    .skill-item {
        height: 84px;
        background-color: #F3F5F8;
        width: 186px;
        padding: 16px 12px 0px;
        border-radius: 12px;
        position: relative;
        cursor: pointer;
        box-sizing: border-box;
        border-radius: 4px;
        overflow: hidden;

        &:hover {
            background-color: #ECF8F6;
        }

        &.selected {
            background-color: #DFF2E8;
        }



        .row1 {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .title {
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 8px;
                line-height: 12px;
                font-weight: 500;

                white-space: nowrap;

                img {
                    width: 18px;
                    height: 18px;
                    border-radius: 4px;
                    object-fit: cover;
                }

            }

            .tag {
                background: #e0f2e9;
                color: #00b55b;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 18px;
                border-radius: 2px 0 0 2px;
                box-sizing: border-box;
                padding: 0 4px;
                font-size: 12px;
                position: absolute;
                right: 0;
                top: 0;
                font-weight: 500;
            }
        }


        .desc {
            color: #6E768C;
            font-size: 12px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.4;
        }
    }
}
</style>