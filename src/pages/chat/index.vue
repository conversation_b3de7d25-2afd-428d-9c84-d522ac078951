<template>
    <Render v-if="isShow"/>
</template>


<script>
import Render from './render.vue'
export default {
    components: {
        Render
    },
    data() { 
        return {
            isShow:true
        }
    },
    watch: {
        '$route.query.conversationId': {
            handler(newVal, oldValue) {
                if (this.$route.query.nt !== '1') {  // 回复消息的不要刷新页面
                    this.flash()
                    this.$store.commit('initSceneFeatureSwitches')
                }
            },
            immediate: true
        }
    },
    methods: {
        flash() { 
            this.isShow = false
            this.$nextTick(() => {
                this.isShow = true
            })
        },
    }
}
</script>