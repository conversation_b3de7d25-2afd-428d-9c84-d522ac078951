import Vue from 'vue'
import store from '@/store';
import VueRouter from 'vue-router'
import ElementUI from 'element-ui'
import '@/element-variables.scss'
import api from '@/utils/api'
import App from './share.vue'
import '@/glob.less'
import utils from '@/utils'

import cpImageViewer from '@/components/cpImageViewer'

Vue.use(VueRouter)
Vue.use(ElementUI, { size: 'small', zIndex: 3000 })
Vue.use(api)
Vue.use(cpImageViewer)

Vue.prototype.$myUtils = utils

// 配置路由
const router = new VueRouter({
    mode: 'hash',
    routes: [
        {
            path: '/',
            name: 'share',
            component: () => import('./share.vue')
        }
    ]
})

new Vue({
    store,
    router,
    render: h => h(App)
}).$mount('#app') 