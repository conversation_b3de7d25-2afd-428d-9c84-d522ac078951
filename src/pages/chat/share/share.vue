<template>
    <div class="cp-msg-list-wrap">
        <div class="scroll-bottom" v-show="showScrollBottom">
            <i class="iconfont el-icon-arrow-down" @click="scrollToBottom(true)"></i>
        </div>
        <div class="cp-msg-list" ref="msgList">
            <div class="msg-list">
                <div class="header-block">
                    <h1 class="title">{{ title }}</h1>
                    <div class="description">{{ createDate }}•内容由 AI 生成，不能完全保障真实</div>
                </div>
                <div class="msg-list-item-wrap" :class="[getMsgType(it)]" v-for="(it, i) in list"
                    :key="it.id || `user_${i}`" :ref="`msg-${i}`">
                    <systemMsg v-if="isSystemMsg(it)" :msg="it"/>
                    <div v-else class="msg-list-item" :class="it.role">
                        <div class="msg-list-right">
                            <div class="bubble" :class="[it.type]">
                                <template v-if="!it.type || it.type === 'LLM'">
                                    <render-reason :msg="it"></render-reason>
                                    <render-content :msg="it"></render-content>
                                </template>
                                <render-other-box v-else :msg="it"></render-other-box>
                                <file-list :list="it.attachments" :readOnly="true"></file-list>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="open-zeus-btn">
            <el-button size="small" type="primary" @click="openZeus" round icon="iconfont icon-lingganguangchang">
                打开ZEUS
            </el-button>
        </div>
    </div>
</template>

<script>
import fileList from '../component/fileList.vue'
import renderContent from '../component/msg/content.vue'
import renderReason from '../component/msg/reason.vue'
import systemMsg from '../component/msg/systemMsg.vue'
import renderOtherBox from '../component/msg/otherBox.vue'


export default {
    components: {
        fileList,
        renderReason,
        renderContent,
        systemMsg,
        renderOtherBox
    },
    data() {
        const that = this
        return {
            list: [],
            showScrollBottom: false,
            title: '',
            createDate: ''
        }
    },
    methods: {
        getMsgType(it) {
            if (this.isSystemMsg(it)) {
                return 'SYSTEM'
            }
            return it.role.USER === 'USER' ? 'USER' : 'ASSISTANT'
        },
        isSystemMsg(it) {
            return ['LLM_BOUNDARY', 'SYSTEM_INFO'].includes(it.type)
        },
        handleScroll() {
            const el = this.$refs.msgList;
            const distanceToBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
            this.showScrollBottom = distanceToBottom > 150;
        },
        setHeaders(res) {
            document.title = res.title

            // 更新 og:title 元标签
            const ogTitleMeta = document.querySelector('meta[property="og:title"]')
            if (ogTitleMeta) {
                ogTitleMeta.setAttribute('content', res.title)
            }

            this.title = res.title
            this.createDate = this.$myUtils.timeFormate(res.createDate, 'yyyy年MM月dd日')
        },
        async initListData() {
            const id = this.$route.query.id
            if (!id) {
                return
            }
            const res = await this.$api.get('/ai-main-app/api/conversationShare/get', { params: { id } })
            this.setHeaders(res)
            this.list = res.messageVOS.map(it => ({ reasonContent: '', content: '', ...it }))
            if (res.ssePending) {
                this.list[this.list.length - 1].isReasonExpanded = true
            }
        },
        toggleReason(item) {
            this.$set(item, 'isReasonExpanded', !item.isReasonExpanded);
        },
        scrollToBottom(isShouldScroll, behavior = 'smooth') {
            const el = this.$refs.msgList;
            if (isShouldScroll) {
                setTimeout(() => {
                    el.scrollTo({
                        top: el.scrollHeight,
                        behavior
                    });
                }, 0);
                return
            }
            if (!this.showScrollBottom) {
                el.scrollTo({
                    top: el.scrollHeight,
                    behavior
                })
            }
        },
        openZeus() {
            window.open(window.location.origin, '_blank');
        }
    },
    created() {
        this.initListData()
    },
    watch: {
        '$route.query.id': {
            handler(newId) {
                if (newId) {
                    this.initListData()
                }
            }
        }
    },
    mounted() {
        this.$refs.msgList.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
        this.$refs.msgList.removeEventListener('scroll', this.handleScroll);
    }
}
</script>

<style lang="less" scoped>
.cp-msg-list-wrap {
    background-color: #fff;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    height: 100vh;

    .scroll-bottom {
        position: absolute;
        right: 10px;
        bottom: 26px;
        right: 10px;
        z-index: 10;

        i {
            border: 1px solid #E6E8EB;
            background-color: #fff;
            font-size: 16px;
            width: 26px;
            height: 26px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10000px;
            box-sizing: border-box;
            cursor: pointer;
        }
    }

    .msg-list-item-wrap {
        width: 100%;
        display: flex;

        &.SYSTEM {
            justify-content: center;
        }

        &.USER {
            justify-content: flex-end;
        }
    }

    .cp-msg-list {
        overflow-y: auto;
        color: #222;
        height: 100%;
        width: 100%;

        .msg-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin: 0 auto;
            width: 100%;
            max-width: 800px;
            box-sizing: border-box;
            padding: 16px;
            padding-bottom: 80px;

            .header-block {
                padding: 0 0 20px 0;
                border-bottom: 1px solid #E6E8EB;
                margin-bottom: 16px;

                .title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #222;
                    margin: 0 0 8px 0;
                }

                .description {
                    font-size: 12px;
                    color: #6E768C;
                }
            }

            .msg-list-item {
                display: flex;
                gap: 12px;
                max-width: 100%;
                animation: fadeIn 0.3s ease-in-out;
                width: 100%;

                .avatar {
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    overflow: hidden;
                    flex-shrink: 0;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                    }
                }

                .msg-list-right {
                    position: relative;
                    max-width: 96%;

                    .bubble {
                        position: relative;
                        border-radius: 8px;
                        word-break: break-word;
                        overflow: auto;
                        max-width: 100%;
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                        &.LLM{
                            padding: 12px;
                        }
                    }
                }

                &.USER {
                    align-self: flex-end;
                    flex-direction: row-reverse;

                    .bubble {
                        background-color: #F6FCF9;
                        border-radius: 16px 0 16px 16px;
                    }
                }

                &.ASSISTANT {
                    align-self: flex-start;

                    .bubble {
                        background-color: #F9FAFB;
                        border-radius: 0 16px 16px 16px; 
                    }
                }
            }
        }
    }

    .open-zeus-btn {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
        text-align: center;

        .el-button {
            font-size: 14px;
            line-height: 16px;
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.reason-content-wrapper {
    .reasonContent {
        transition: all 0.3s ease;
        overflow: hidden;
        max-height: 0;
        opacity: 0;

        &.show {
            margin-top: 8px;
            max-height: 2000px;
            opacity: 1;
        }
    }
}
</style>


<style lang="less">
:not(pre)>code[class*="language-"],
pre[class*="language-"] {
    background: #2d2d2d;
    overflow-x: auto;
    max-width: calc(100vw - 150px);
}
</style>