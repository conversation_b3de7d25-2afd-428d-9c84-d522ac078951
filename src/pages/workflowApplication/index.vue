<template>
  <div class="page-workflow">
    <div class="top">
      <div class="title">艺境ArtScape</div>
      <el-tabs v-model="activeTab" @tab-click="tabClick">
        <el-tab-pane
          :label="tab.name"
          :name="tab.id"
          v-for="(tab, i) in tabList"
          :key="i"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="list hover-scroll">
      <div
        class="item"
        v-for="(it, i) in data"
        :key="i"
        @click="$router.push({ name: 'workflowBuild', query: { id: it.id } })"
      >
        <div class="img-wrap">
          <el-image :src="$myUtils.generateOssImageUrl(it.imageUrl)" />
        </div>
        <div class="name">{{ it.name }}</div>
        <div class="desc" :title="it.description">{{ it.description }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: null,
      tabList: [],
      activeTab: "-1",
    };
  },
  async created() {
    this.getTabList();
    this.getData();
  },
  methods: {
    async getTabList() {
      const list = await this.$api.post(
        "/ai-main-app/api/comfyCategory/listAll",
        { name: "" }
      );
      this.tabList = [{ name: "全部", id: "-1" }, ...list];
    },
    tabClick(tab) {
      this.activeTab = tab.name;
      this.getData();
    },
    async getData() {
    const currentTab = this.activeTab;

    const params = {
      data: {
        status: "1",
        categoryId: currentTab === "-1" ? "" : currentTab,
      },
      orderBy: "update_date",
      pageNo: 0,
      pageSize: 99999,
    };

    try {
      const res = await this.$api.post("/ai-main-app/api/comfyWorkflow/list", params);
      if (this.activeTab === currentTab) {
        this.data = res.list;
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  },
  },
};
</script>
<style lang="less">
.page-workflow {
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .top{
    height: 80px;
    // width: 100%;
    background: #ffffff;
    margin: 0;
  }
  .title {
    text-align: center;
    
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .block-title { 
    font-size: 24px;
    font-weight: 900;
    line-height: normal;
  }
  & > .el-tabs {
    margin-top: 16px;
    .el-tabs__nav-scroll {
      display: flex;
      justify-content: center;
    }
  }
  .list {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 24px;
    margin-top: 18px;
    max-height: calc(100vh - 200px);
    padding: 10px 5vw;
    width: 100%;
    box-sizing: border-box;
    min-width: 900px;
    margin-left: 12px;
    overflow-y: auto;
    .item {
      flex: 0 0 calc(20% - 24px); // 大屏幕下每行5个item
      max-width: calc(20% - 24px); 
      font-size: 16px;
      font-style: normal;
      line-height: normal;
      border-radius: 12px;
      background: #f7f8fa;
      padding: 16px 0;
      cursor: pointer;
      box-sizing: border-box;
      // max-height: 250px;
      &:hover {
        box-shadow: 0 0 10px 1px rgba(0, 0, 0, 0.2);
        img {
          transform: scale(1.1);
        }
      }
      .img-wrap {
        padding: 0 12%;
      }
      .el-image {
        // width: 170px;
        // height: 129px;
        flex-shrink: 0;
        border-radius: 12px;
        overflow: hidden;
        // margin-left: 32px;
        img {
          transition: all 0.3s;
        }
      }
      .name {
        font-size: 14px;
        padding: 16px 16px 0 16px;
        font-weight: 600;
        
      }
      .desc {
        box-sizing: border-box;
        font-size: 12px;
        color: #6e768c;
        margin-top: 4px;
        padding-left: 16px;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  @media (max-width: 1440px) {
    .list .item {
      flex: 0 0 calc(25% - 24px); // 小屏幕下每行4个item
      max-width: calc(25% - 24px);
    }
  }
}
</style>