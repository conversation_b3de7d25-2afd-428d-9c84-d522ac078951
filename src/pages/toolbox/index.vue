<template>
  <div class="page-toolbox">
    <div class="list">
      <div class="item" v-for="(it, i) in list" :key="i">
        <div class="content" @click="openExt(it.platformCode)">
          <img :src="it.imageUrl" alt="">
          <div class="right">
            <div class="row1">
              <div class="title">{{ it.title }}</div>
              <div class="number">{{ getExtTextInfo(it.platformCode) }}</div>
            </div>
            <div class="desc">{{ it.desc }}</div>
          </div>
        </div>
        <div class="tag">免密登录</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
  },
  data() {
    return {
      extInfo: null
    };
  },
  computed: {
    list() {
      return [
        {
          platformCode: 'MID_JOURNEY',
          title: 'MidJourney',
          desc: 'AI图像生成工具',
          imageUrl: 'https://oss.syounggroup.com/bigfile/defaultTenantId/********-144244.png',
          noPassword: true
        }
      ]
    }
  },
  methods: {
    getExtTextInfo(platformCode) {
      if (!this.extInfo) {
        return
      }
      const item = this.extInfo.find(it => it.platformCode === platformCode)
      if (!item) {
        return
      }
      return `登录人数 ${item.usingCount}/${item.totalLimitCount}`
    },
    async getExtInfo() {
      const res = await this.$api.get('/hestia-service/api/crawlerAccount/statUserCrawlerAccountData')
      this.extInfo = res
    },
    async checkExt() {
      const res = await this.$api.get('/user/api/user/self?target=test', { headers: { 'retHeader': 'true' }, })
      const headers = res.headers
      const hasExt = headers['content-type'] === 'application/json;charset=UTF-8;X-server=crawler'
      if (!hasExt) {
        this.$confirm('检测到您还没安装浏览器插件，要先安装插件才能使用快捷登录功能，如何安装请看：<a target="_blank" style="color:#00B55B" href="https://syounggroup.feishu.cn/wiki/F1aWwN637ihs8jk0LPacWbuqnJc">账号共享插件安装手册</a>', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
      return hasExt;

    },
    async openExt(platformCode) {
      this.getExtTextInfo()
      const hasExt = await this.checkExt()
      if (!hasExt) return
      this.retryCount = 0;
      this.getExt(platformCode)

    },
    async getExt(platformCode) {
      let ret = await this.$api.get('/hestia-service/api/crawlerAccount/searchAccountByPlatform', { params: { platformCode } })
      if (ret.platformCode === undefined) {
        this.$message.error('找不到可以使用的账号，请联系管理员绑定账号!')
        return
      }
      if (ret.cookies === undefined) {
        this.retryCount++
        if (this.retryCount <= 5) {
          if (this.retryCount === 1) {
            this.$message.error('当前登录人数较多，正在为您分配账号，预计需要2分钟，请耐心等待。')
          }
          setTimeout(() => this.getExt(platformCode), 40000)
        } else {
          this.$message.error('当前登录人数过多，请稍后再试!')
        }
        return
      }

      this.$api.post('/user/api/user/self', {
        data: ret.cookies,
        userAgent: ret.userAgent,
        target: ret.platformCode,
        loginName: ret.loginName,
        accountId: ret.accountId,
        accountName: ret.accountName,
        extensionVersion: ret.extensionVersion
      }, {
        params: {
          target: encodeURIComponent(ret.platformCode),
          loginName: encodeURIComponent(ret.loginName),
          accountId: encodeURIComponent(ret.accountId),
          accountName: encodeURIComponent(ret.accountName),
          extensionVersion: encodeURIComponent(ret.extensionVersion)
        }
      })


    }
  },
  mounted() {
    this.getExtInfo();
  }
};
</script>
<style lang="less">
.page-toolbox {
  padding: 24px;
  height: 100%;
  box-sizing: border-box;

  .block-title { 
    font-size: 24px;
    font-weight: 900;
    line-height: normal;
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;

    .item {
      position: relative;
      width: 370px;

      .tag {
        position: absolute;
        background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
        border-radius: 38px;
        padding: 3px 5px;
        right: 0;
        top: 0;
        font-size: 12px;
      }

      .content {
        display: flex;
        padding: 26px;
        background: #171E22;
        border-radius: 12px;
        cursor: pointer;

        img {
          width: 56px;
          height: 56px;
          margin-right: 24px;
        }

        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .row1 {
            display: flex;
            align-items: center;

            .title {
              color: #FFF;
              font-size: 14px;
              font-weight: 600;

            }

            .number {
              color: #00B55B;
              font-size: 12px;
              margin-left: 12px;
            }
          }


          .desc {
            font-size: 14px;
            color: #828588;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: 8px;
          }
        }
      }
    }
  }
}
</style>