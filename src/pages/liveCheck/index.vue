<template>
  <div class="page-live-check">
    <div class="content">
      <div class="row1">
        <el-tabs type="border-card" class="left" v-model="activeTab">
          <el-tab-pane label="Excel检查" name="file">
            <el-button type="primary" @click="downFile">下载模板</el-button>
            <el-upload class="upload-demo" drag action="" :show-file-list="false" :http-request="httpRequest">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </el-tab-pane>
          <el-tab-pane label="文本检查" name="text">
            <el-input class="check-text" type="textarea" placeholder="请输入内容" v-model="checkText" :rows="9"
              resize="none">
            </el-input>
          </el-tab-pane>

        </el-tabs>
        <el-card class="right" v-html="scriptsHtml">
        </el-card>
      </div>
      <el-card class="opt-btns">
        <el-button type="primary" @click="startCheck"
          :disabled="taskStatus === '-2' || taskStatus === '0'">开始检查</el-button>
        <el-button type="primary" @click="stopCheck" :disabled="true">停止检查</el-button>
      </el-card>
      <el-card class="row2">
        <div class="title">检查结果：{{ statusText }}<span class="progress">{{ progress }}</span></div>
        <el-table :data="tableData" border style="width: 100%" height="calc(100vh - 180px)" min v-loading="loading"
          :span-method="arraySpanMethod">
          <el-table-column prop="text" label="语句" width="180">
          </el-table-column>
          <el-table-column prop="typeName" label="语句类型" width="180">
          </el-table-column>
          <el-table-column prop="izViolation" label="是否违规" width="80">
            <template slot-scope="scope">
              {{ scope.row.izViolation ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="violationWords" label="违规词语">
          </el-table-column>
          <el-table-column prop="violationTypes" label="违规类型">
          </el-table-column>
          <el-table-column prop="violationConfidence" label="置信度" width="60">
          </el-table-column>
          <el-table-column prop="violationReason" label="违规理由">
          </el-table-column>
          <el-table-column prop="improveSuggestion" label="改进建议">
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>
<script>

export default {
  data() {
    return {
      excelData: null,
      taskRes: null,
      resultRes: null,
      activeTab: 'file',
      checkText: ''
    };
  },
  computed: {
    progress() {
      if (!this.resultRes) {
        return ''
      }
      return `（${this.resultRes.filter(it => it.status !== '0').length} / ${this.resultRes.length}）`
    },
    scriptsHtml() {
      if (!this.excelData) {
        return ''
      }
      let html = this.excelData.map((text, i) => {
        if (!this.resultRes) {
          return `<div class="row">${text}</div>`
        }
        const taskId = this.taskRes.resultList[i].id
        const task = this.resultRes.find(it => it.id === taskId)
        if (!task) {
          return `<div class="row">${text}</div>`
        }
        const errTexts = task.sections.map(it => it.violationWords).flat();
        const regex = new RegExp(`(${errTexts.join('|')})`, 'g');
        text = text.replace(regex, '<span class="err-text">$1</span>')
        return `<div class="row">${text}</div>`
      }).join('')
      return html
    },
    taskStatus() {
      if (this.activeTab === 'file' && !this.excelData) {
        return '-2'
      }
      if (this.activeTab === 'text' && !this.checkText) {
        return '-2'
      }

      if (!this.taskRes) {
        return '-1'
      }
      if (!this.resultRes) {
        return '0'
      }
      if (this.resultRes.find(it => it.status === '0')) {
        return '0'
      }
      return '1'
    },
    statusText() {
      return {
        '-2': this.activeTab === 'file' ? '待上传文件' : '待输入文本',
        '-1': '待检查',
        '0': '进行中',
        '1': '成功',
        '2': '失败'
      }[this.taskStatus]
    },
    tableData() {
      if (!this.resultRes) {
        return []
      }
      return [].concat.apply([], this.resultRes.map(it => it.sections));
    }
  },
  created() {
  },
  methods: {
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return this.spanMethod('text', { row, column, rowIndex, columnIndex });
      }
      // if (columnIndex === 1) {
      //   // 只有在第一列相同的情况下，才检查第二列
      //   const previousRow = this.tableData[rowIndex - 1];
      //   if (previousRow && previousRow['text'] === row['text']) {
      //     return this.spanMethod('typeName', { row, column, rowIndex, columnIndex });
      //   }
      // }
      return [1, 1]; // 默认不合并
    },
    spanMethod(key, { row, column, rowIndex, columnIndex }) {
      const currentName = row[key];
      const previousRow = this.tableData[rowIndex - 1];
      if (previousRow && previousRow[key] === currentName) {
        return [0, 0]; // 不显示当前单元格
      }
      let rowSpan = 1;
      for (let i = rowIndex + 1; i < this.tableData.length; i++) {
        if (this.tableData[i][key] === currentName) {
          rowSpan++;
        } else {
          break;
        }
      }
      return [rowSpan, 1];
    },
    downFile() {
      window.open('https://oss.syounggroup.com/bigfile/defaultTenantId/直播脚本违规检测模板.xlsx')
    },
    async httpRequest({ file }) {
      const formData = new FormData();
      formData.append('file', file);
      const res = await this.$api.post('/ai-main-app/api/liveStreamScript/importDouyin', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      this.excelData = res
    },
    async startCheck() {
      if (this.activeTab === 'text') {
        this.excelData = [this.checkText]
      }
      const res = await this.$api.post('/ai-main-app/api/liveStreamScript/checkDouyinScriptAsync', {
        scripts: this.excelData
      });
      this.taskRes = res
      this.getTableData()
    },
    sleep(t) {
      return new Promise(r => setTimeout(r, t))
    },
    async getTableData() {
      if (this.taskStatus === '1' || this.taskStatus === '2') {
        return
      }
      const res = await this.$api.post('/ai-main-app/api/liveStreamScript/getDouyinImportResult', null, {
        params: {
          /// '914539645152785410' || 
          taskId: this.taskRes.taskId
        }
      });
      this.resultRes = res
      await this.sleep(5000)
      this.getTableData()
    }
  },
};
</script>
<style lang="less">
.page-live-check {
  padding: 24px;
  height: 100%;
  box-sizing: border-box;

  .block-title { 
    font-size: 24px;
    font-weight: 900;
    line-height: normal;
  }

  .opt-btns {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    gap: 16px;

    .el-card__body {
      padding: 16px;
    }
  }

  .content {
    margin-top: 16px;

    .row1 {
      display: flex;
      gap: 40px;
      align-items: stretch;

      .left,
      .right {
        background-color: #FFF;
        height: 293px;
        box-sizing: border-box;
      }

      .left {
        flex: 0 0 420px;

        .el-upload {
          margin-top: 18px;
        }

        .check-text {

          textarea {
            color: #303133 !important;
          }
        }
      }

      .right {
        flex: 1;
        font-size: 12px;
        color: #606266;
        line-height: 1.5;
        overflow: auto;
        padding: 24px;

        .row {
          text-indent: 2em;
          margin-bottom: 1em;

          .err-text {
            color: #F56C6C;
            font-weight: bold;
          }
        }

      }
    }

    .row2 {
      margin-top: 18px;

      .title {
        .progress {
          margin-left: 10px;
        }
      }


      .el-table {
        margin-top: 18px;
      }
    }
  }
}
</style>