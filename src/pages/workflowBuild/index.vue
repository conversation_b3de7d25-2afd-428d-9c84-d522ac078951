<template>
  <div class="page-workflow-build" v-if="data">
    <cp-header :title="data.name" :handerBack="handerBack" :helpWiki="data.userGuide" />
    <div class="main">
      <div class="left">
        <div class="cps-wrap hover-scroll">
          <component class="cps" :ref="cp.key" v-for="cp in data.userInputJson" :key="cp.key"
            v-bind:is="`cps-${cp.bizType}`" :bind="cp" v-model="values[cp.key]" />
        </div>
        <div class="build">
          <div class="btn1" @click="build">立即生成</div>
          <el-checkbox class="feishu-message" v-model="enableFeishuNotice">生成完成后飞书通知我</el-checkbox>
        </div>
      </div>
      <div class="right hover-scroll" :class="{ empty: !list.length }">
        <cp-empty text="暂无作品，快去创作AI绘图作品吧～" v-if="!list.length" />
        <div class="img-list" v-else>
          <div class="img-item" v-for="(it, i) in list" :key="it.comfyWorkflowTaskVO.id">
            <div class="row1">
              <div class="row1-left">
                <div class="name">{{ data.name }}</div>
                <div class="time" v-if="it.comfyWorkflowTaskVO.runEndTime">{{
                  $myUtils.formatTimestamp(it.comfyWorkflowTaskVO.runEndTime) }}</div>
              </div>
              <div class="row1-right">
                <el-popover v-if="it.comfyWorkflowTaskVO.status === 'COMPLETED'" placement="bottom" width="auto"
                  trigger="hover" content="发布至灵感广场">
                  <i slot="reference" class="iconfont icon-fenxiang" @click="shareGround(it)"></i>
                </el-popover>

                <el-popover placement="bottom" width="auto" trigger="hover"
                  content="点击复用创意后，会将生成时配置的文字、图片等信息重新填回左侧，您可以重新在左侧进行内容编辑">
                  <i slot="reference" class="iconfont icon-fuzhicanshu"
                    @click="copyHis(it.comfyWorkflowTaskVO.userInputs)"></i>
                </el-popover>

                <el-popover placement="bottom" width="auto" trigger="hover" content="删除该批次">
                  <i slot="reference" class="iconfont icon-shanchurenwu" @click="delHis(it)"></i>
                </el-popover>
              </div>
            </div>
            <div class="prompt">{{ getPrompt(it) }}</div>
            <div class="imgs">
              <div class="fail" v-if="it.comfyWorkflowTaskVO.status === 'FAILED'">
                <i class="iconfont icon-shengtushibai"></i>
                <div class="text">
                  <cp-link text="生图失败，点击联系@一德" openId="ou_7a5c95a507e99740f1e12bb1a3c98b04"></cp-link>
                </div>
              </div>
              <div class="progress" v-else-if="!it.comfyWorkflowTaskVO.outputImageUrls">
                <el-progress type="circle" :percentage="getPercentage(it.comfyWorkflowTaskVO)" :width="60"
                  :stroke-width="5" color="#00B55B" define-back-color="rgba(255, 255, 255, 0.19)"></el-progress>
                <div class="text">生成中，预计需要{{ getRemainTime(it.comfyWorkflowTaskVO) }}s</div>
              </div>
              <div v-for="(url, j) in it.comfyWorkflowTaskVO.outputImageUrls" :key="j">
                <video class="video-build" v-if="url.endsWith('.mp4')" :src="url" controls
                  @click.stop="previewImg(i, j)"></video>
                <el-image lazy v-else class="img-build zoom" :key="j" :src="$myUtils.generateOssImageUrl(url)"
                  @click="previewImg(i, j)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="serviceStop" :show-close="false" custom-class="robot-dialog">
      <div class="content">
        <i class="close el-icon-close" @click="closeRobotDialog"></i>
        <img src="../../assets/robot.png" class="robot" />
        <div class="title">温馨提示</div>
        <div class="msg">服务器正在维护中，请晚些时候再试哦～</div>
        <div class="btn" @click="closeRobotDialog">我知道了</div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import utils from '../../utils'
import cps from './cps/index.js'
import { goFeishuUser } from '@/utils/feishu'

async function getList() {
  const res = await this.$api.post('/ai-main-app/api/comfyWorkflowLog/list', {
    data: {
      workflowId: this.id
    },
    "orderBy": "id",
    "pageNo": 1,
    "pageSize": 999
  })
  this.list = res.list || []
}

export default {
  components: {
    ...cps
  },
  data() {
    return {
      serviceStop: false,
      list: [],
      enableFeishuNotice: false,
      data: null,
      values: {},
      dateNow: Date.now()
    };
  },
  created() {
    this.flashDate()
    this.getServiceStatus()
    this.getData()
    this.getList()
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  methods: {
    initArgs() {
      if (this.$route.params.userInputs) {
        this.copyHis(this.$route.params.userInputs)
      } else {
        this.initValues()
      }
    },
    copyHis(userInputs) {
      this.initValues()
      const values = {}
      userInputs.forEach(it => {
        if (this.values[it.key] !== undefined) {
          values[it.key] = it.value
        }
      })
      this.values = { ...this.values, ...values }
      this.copyHisWarning(values)

    },
    async shareGround(it) {
      await this.$confirm('是否分享至灵感广场？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      const res = await this.$api.post('/ai-main-app/api/comfyTaskShare/shareTask', {
        taskId: it.comfyWorkflowTaskVO.id,
        imageUrl: it.comfyWorkflowTaskVO.outputImageUrls[0]
      })
      this.$message.success('分享成功')
    },
    copyHisWarning(values) {
      const keys = this.data.userInputJson.filter(it => it.bizType === 'IMAGE' || it.bizType === 'PROMPT_TEXT').map(it => it.key)
      for (const key of keys) {
        if (!values[key]) {
          this.$message.warning(`注意：部分参数已成功复用，但检测到关键参数（如提示词、图片）存在不匹配。请仔细检查并手动调整。`)
          return
        }
      }
    },
    handerBack() {
      this.$router.back()
    },
    flashDate() {
      this.timer = setInterval(() => {
        this.dateNow = Date.now()
      }, 800);
    },
    getRemainTime(it) {
      const startTime = it.createDate
      const numb = it.comfyTaskPredictResult.waitSeconds - (this.dateNow - startTime) / 1000
      if (numb <= 0) {
        this.getListThrottle()
        return 1
      }
      return Math.floor(numb)
    },
    getPercentage(it) {
      const startTime = it.createDate
      const percent = Math.floor((this.dateNow - startTime) / 1000 / it.comfyTaskPredictResult.waitSeconds * 100)
      if (percent >= 100) {
        return 99
      }
      return Math.max(percent, 0)
    },
    closeRobotDialog() {
      this.serviceStop = false
    },
    async getServiceStatus() {
      const res = await this.$api.get('/ai-main-app/api/comfyWorkflowServer/checkStatus')
      this.serviceStop = !res
    },
    getPrompt(item) {
      return item.comfyWorkflowTaskVO.userInputs.find(it => it.bizType === 'PROMPT_TEXT')?.value
    },
    async getData() {
      const res = await this.$api.get('/ai-main-app/api/comfyWorkflow/get', {
        params: {
          id: this.id
        }
      })
      this.data = res
      this.initArgs()
    },
    async delHis(it) {
      await this.$confirm('删除后图片不可恢复，确定要删除吗？', '删除任务', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      await this.$api.post('/ai-main-app/api/comfyWorkflowLog/deleteByIds', [it.id])
      await this.getList()
    },
    initValues() {
      const values = {}
      this.data.userInputJson.forEach(it => {
        values[it.key] = it.value
      })
      this.values = values
    },
    getList: getList,
    getListThrottle: utils.throttle(getList, 4000)
    ,
    isPromptHasValue() {
      const promptItem = this.data.userInputJson.find(it => it.bizType === 'PROMPT_TEXT')
      if (!promptItem) {
        return true
      }
      return this.values[promptItem.key]
    },
    build: utils.debounce(async function () {
      if (!this.data.status === 0) {
        this.$message.error('该功能已停用，如有需求请联系一德或星剑')
        return
      }
      if (!this.isPromptHasValue()) {
        this.$message({
          message: '请输入提示词后提交',
          type: 'error'
        });
        return
      }
      if (!this.checkMaskProp()) {
        return
      }
      const userInputs = []
      for (let key in this.values) {
        userInputs.push({
          key,
          value: this.values[key]
        })
      }
      await this.$api.post('/ai-main-app/api/comfyWorkflowTask/submitTask', {
        enableFeishuNotice: this.enableFeishuNotice ? '1' : '0',
        userInputs,
        workflowId: this.id
      })
      await this.getList()
    }, 1000),
    previewImg(i, j) {
      this.$previewArg(i, j, this.list.map(it => ({ ...it.comfyWorkflowTaskVO, id: it.id })), { copyHis: this.copyHis, delHis: this.delHis })
    },
    checkMaskProp() {
      const item = this.data.userInputJson.find(it => it.bizType === 'MASK_IMAGE')
      if (item && this.values[item.key].split(',').filter(_ => _).length !== 3) {
        this.$message({
          message: `请先给${item.keyName}绘制蒙版`,
          type: 'error'
        });
        return false
      }
      return true
    },
    goFeishuUser:goFeishuUser
  }
};
</script>
<style lang="less">
.page-workflow-build {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;




  .el-input,
  textarea {

    &:hover,
    &:focus {
      border: 1px solid #00B55B !important;
    }
  }

  .main {
    box-sizing: border-box;
    flex: 1;
    padding: 16px;
    display: flex;
    height: 100%;
    max-height: calc(100vh - 56px);
    background-color: #F3F5F8;

    .left {
      box-sizing: border-box;
      display: flex;
      height: 100%;
      flex-direction: column;
      border-radius: 12px;
      border: 1px solid #E6E8EB;
      background-color: #fff;
      margin-right: 11px;
      position: relative;

      .cps-wrap {
        padding: 16px;
        flex: 1;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        width: 398px;
        box-sizing: border-box;
        padding-bottom: 120px;

        .cps {
          margin-bottom: 30px;
          width: 366px;
        }



        // 重写全局上传
        .el-upload {

          .el-image__placeholder {
            background-color: transparent;
          }

          .el-upload-dragger {
            border-radius: 8px;
            border: 1px solid #e6e8eb !important;
            background: rgba(255, 255, 255, 0.06);
            width: 366px;
            height: 133px;

            &:hover,
            &:focus {
              border: 1px solid #00B55B !important;
            }
          }

          .upload-box {
            width: 100%;
            height: 100%;

            .iconfont {
              font-size: 36px;
              margin: 36px 0 4px;
              display: inline-block;
              color: #9ea4b2;
            }

            .el-upload__text {
              color: #9ea4b2;
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
            }

          }

          .percentage-box {
            width: 100%;
            height: 100%;
            position: relative;
            pointer-events: none;
            text-align: center;

            .el-progress {
              width: 220px;
              margin: auto;

              .el-progress-bar__outer {
                background-color: rgb(235, 238, 245);
              }

              .el-progress-bar__inner {
                background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
              }
            }

            .text {
              color: #7F7F7F;
              font-size: 14px;
              margin-top: 49px;
              margin-bottom: 12px;
            }
          }

          .preview-box {
            width: 100%;
            height: 100%;
            position: relative;
            cursor: default;

            .el-icon-close {
              position: absolute;
              width: 20px;
              height: 20px;
              background-color: #F3F5F8;
              color: #9EA4B2;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 100vw;
              top: 8px;
              right: 8px;
              cursor: pointer;
              font-size: 12px;
            }

            .el-image {
              width: 100%;
              height: 100%;

              &.mask {
                position: absolute;
                top: 0;
                left: 0;
              }
            }

            .btn3 {
              position: absolute;
              left: 10px;
              bottom: 8px;
            }
          }
        }

      }

      .build {
        position: absolute;
        bottom: 0;
        left: 0px;
        padding: 20px 0;
        z-index: 1002;
        width: 366px;
        padding-right: 6px;
        padding-left: 16px;
        background-color: #fff;
        border-radius: 18px;


        .feishu-message {
          margin-top: 12px;

          .el-checkbox__input {
            .el-checkbox__inner {
              border-radius: 2px;
            }

          }

          .el-checkbox__label {
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }

          .el-checkbox__input.is-checked+.el-checkbox__label {}
        }
      }
    }

    .right {
      box-sizing: border-box;
      flex: 1;
      display: flex;
      background-color: #fff;
      border-radius: 12px;
      border: 1px solid #E6E8EB;
      overflow: auto;
      height: 100%;
      padding: 24px;
      flex-direction: column;

      .cp-empty {
        margin: 200px auto 0;
      }

      .img-list {
        width: 100%;

        .img-item {
          margin-bottom: 32px;

          .row1 {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 4px;
            width: 100%;

            .row1-left {
              display: flex;
              align-items: flex-start;

              .name {
                font-size: 14px;
                font-style: normal;
                font-weight: 900;
                line-height: normal;
                margin-right: 8px;
              }

              .time {
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
              }
            }

            .row1-right {
              display: flex;
              gap: 16px;

              i {
                color: #7F7F7F;
                font-size: 16px;
                cursor: pointer;
              }
            }




          }

          .prompt {
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 16px;
            color: #9EA4B2;
          }

          .imgs {
            display: flex;
            flex-wrap: wrap;

            .fail {
              width: 210px;
              height: 210px;
              border-radius: 12px;
              background: #F3F5F8;
              text-align: center;
              padding-top: 57px;
              box-sizing: border-box;

              .iconfont {
                font-size: 56px;
                color: #9EA4B2;
              }

              .text {
                margin-top: 9px;
                color: #9EA4B2;
                text-align: center;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
              }

            }

            .img-build {
              cursor: pointer;
              border-radius: 12px;
              width: 210px;
              margin-right: 12px;
              margin-bottom: 0;

              .el-image__error {
                min-height: 210px;
              }
            }

            .video-build {
              cursor: pointer;
              border-radius: 12px;
              width: 210px;
              height: 210px;
              margin-right: 12px;
              margin-bottom: 0;
            }

            .progress {
              width: 210px;
              height: 210px;
              border-radius: 12px;
              background-color: #F3F5F8;
              text-align: center;
              color: #9EA4B2;

              .el-progress {
                margin-top: 62px;

                .el-progress__text {
                  font-size: 14px !important;
                }
              }

              .text {
                text-align: center;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                margin-top: 8px;
              }
            }
          }




        }
      }
    }
  }

  .robot-dialog {
    border-radius: 16px;
    border: 5px solid rgba(255, 255, 255, 0.77);
    background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
    width: 432px;
    height: 228px;
    position: relative;
    top: -50px;

    .el-dialog__header {
      display: none !important;
    }

    .el-dialog__body {
      padding: 0;
    }



    .content {
      text-align: center;

      .robot {
        width: 132px;
        height: 194px;
        position: absolute;
        top: -130px;
        left: 0px;
      }

      .title {
        color: #08493E;
        text-align: center;
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-top: 60px;
      }

      .msg {
        color: #08493E;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-top: 10px;
      }

      i.close {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 18px;
        right: 18px;
        background-color: #F3F5F8;
        color: #9EA4B2;
        border: 1px solid #fff;
        box-sizing: border-box;
        border-radius: 100vw;
        cursor: pointer;

        &:hover {
          opacity: 0.95;
        }

      }

      .btn {
        box-sizing: border-box;
        display: flex;
        width: 132px;
        height: 47px;
        padding: 10px;
        justify-content: center;
        align-items: center;
        border-radius: 140px;
        background: rgba(255, 255, 255, 0.50);
        cursor: pointer;
        margin: auto;
        margin-top: 25px;
        color: #08493E;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;

        &:hover {
          background: rgba(255, 255, 255, 0.72);
        }
      }
    }
  }
}
</style>