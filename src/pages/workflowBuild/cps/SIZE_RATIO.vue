<template>
    <div class="cp-cps-size-ratio">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <div class="content">
            <el-popover v-for="it in list" :key="it.tips" placement="top" trigger="hover" :content="it.tips"
                popper-class="inline">
                <div slot="reference" class="btn" :class="{ active: value === it.label }"
                    @click="$emit('input', it.label)">
                    <i class="iconfont" :class="it.icon"></i>
                    <span>{{ it.label }}</span>
                </div>

            </el-popover>


        </div>

    </div>
</template>

<script>
export default {
    name: 'cps-SIZE_RATIO',
    props: {
        value: {},
        bind: {}
    },
    computed: {
        list() {
            return [
                {
                    label: '1:1',
                    tips: '1024 x 1024',
                    icon: 'icon-a-1bi1'
                },
                {
                    label: '16:9',
                    tips: '1360 x 768',
                    icon: 'icon-a-16bi9'
                },
                {
                    label: '9:16',
                    tips: '768 x 1360',
                    icon: 'icon-a-9bi16'
                },
                {
                    label: '4:3',
                    tips: '1152 x 864',
                    icon: 'icon-a-4bi3'
                },
                {
                    label: '3:4',
                    tips: '864 x 1152',
                    icon: 'icon-a-3bi4'
                }
            ]
        },
        myValue() {
            return this.value - 0
        }
    },
    created() {
    },
    methods: {
    }
}
</script>

<style lang="less">
.cp-cps-size-ratio {
    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .content {
        display: flex;
        align-items: center;
        flex-direction: row;
        justify-content: space-between;

        .btn {
            font-size: 14px;
            font-weight: 500;
            width: 64px;
            height: 35px;
            box-sizing: border-box;
            flex: 0 0 64px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 100vw;
            background: rgba(255, 255, 255, 0.11);
            
            cursor: pointer;

            i {
                font-size: 16px;
                margin-right: 4px;
            }

            &:hover {
                background: rgba(255, 255, 255, 0.25);
            }

            &.active {
                background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
                color: #08493E;
            }
        }


    }
}
</style>