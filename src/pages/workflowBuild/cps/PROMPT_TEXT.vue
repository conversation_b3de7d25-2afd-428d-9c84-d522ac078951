<template>
    <div class="cp-cps-prompt">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <div class="content-wrap">
            <el-input type="textarea" placeholder="描绘你脑海中的创意，进行图片创作" :value="value" @input="$emit('input', $event)"
                maxlength="500" show-word-limit resize="none">
            </el-input>
            <div class="btns-wrap">
                <el-popover placement="bottom" width="auto" trigger="hover" content="上传图片，根据图片画面内容描述成提示词（图生文）">
                    <div class="btn3" slot="reference" @click="imgDescDialogVisible = true">
                        <i class="iconfont icon-tupianfantui"></i>
                        <span>图片反推</span>
                    </div>
                </el-popover>
                <el-popover placement="bottom" width="auto" trigger="hover" content="没有灵感？让DeepSeek来帮你扩写润色！">
                    <div class="btn3" slot="reference" @click="openKuoxieDialog">
                        <i class="iconfont icon-kuoxierunse"></i>
                        <span>扩写润色</span>
                    </div>
                </el-popover>
                <!-- <el-popover placement="bottom" width="auto" trigger="hover" content="点击查看提示词教程">
                    <div class="btn3" slot="reference" @click="openWiki">
                        <i class="iconfont icon-bangzhushouce"></i>
                        <span>提示词教程</span>
                    </div>
                </el-popover> -->
            </div>
        </div>

        <el-dialog title="扩写结果" :visible.sync="kuoxieDialogVisible" custom-class="cp-cps-prompt-kuoxie-mask"
            width="752px" top="0" append-to-body>
            <div class="content">
                <cp-loading-text loading-text="正在生成中" :text="kuoxieText" :isLoading="kuoxieIsLoading" />
            </div>
            <div slot="footer" class="dialog-footer">
                <div class="btn2 small" @click="cancel">取消</div>
                <div class="btn1 small" @click="kuoXieconfirm" :class="{ disabled: kuoxieIsLoading || !kuoxieText }">填入
                </div>
            </div>
        </el-dialog>

        <el-dialog title="图片反推" :visible.sync="imgDescDialogVisible" custom-class="cp-cps-prompt-img-desc-mask"
            width="597px" top="0" append-to-body>
            <div class="content">
                <div class="upload-box">
                    <el-upload :fileList="fileList" drag action="" :http-request="httpRequest" :limit="2"
                        :show-file-list="false" :before-upload="beforeUpload">
                        <div v-if="percentage !== false" class="percentage-box">
                            <div class="text">图片上传中</div>
                            <el-progress :percentage="percentage" :show-text="false" :strokeWidth="4"></el-progress>
                        </div>
                        <div v-if="imgUrl" class="preview-box">
                            <el-image :src="imgUrl" fit="contain" />
                            <i class="el-icon-close" @click.stop="delImgUrl"></i>
                        </div>
                        <div v-else class="upload-box">
                            <i class="iconfont icon-shangchuantupian"></i>
                            <div class="el-upload__text">拖拽文件到这里或点击上传</div>
                        </div>
                    </el-upload>
                </div>
                <div class="desc-box">
                    <div class="title">描述结果</div>
                    <cp-loading-text loadingText="正在生成中..." :text="imgDesText" :isLoading="imgDesIsLoading" />
                </div>
            </div>
            <div slot="footer" class="dialog-footer">
                <div class="btn2 small" @click="cancel">取消</div>
                <div class="btn1 small" @click="imgDescConfirm" :class="{ disabled: imgDesIsLoading || !imgDesText }">填入
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    name: 'cps-PROMPT_TEXT',
    props: {
        value: {},
        bind: {}
    },
    data() {
        return {
            kuoxieDialogVisible: false,
            imgDescDialogVisible: false,
            imgUrl: '',
            percentage: false,
            kuoxieText: '',
            kuoxieIsLoading: true,
            imgDesText: '',
            imgDesIsLoading: false
        }
    },
    computed: {
        fileList() {
            if (!this.imgUrl) {
                return []
            }
            return [{ url: this.imgUrl }]
        },
    },
    created() {
    },
    methods: {
        openWiki() {
            window.open('https://syounggroup.feishu.cn/docx/HtmRdQQaXonyNNxJSExcSsUdnKb')
        },
        openKuoxieDialog() {
            if (!this.value) {
                this.$message.error('请先填写提示词')
                return
            }
            this.kuoxieDialogVisible = true
            this.getKuoxie()
        },
        cancel() {
            this.kuoxieDialogVisible = false
            this.imgDescDialogVisible = false
        },
        kuoXieconfirm() {
            this.$emit('input', this.kuoxieText)
            this.cancel()
            setTimeout(() => {
                this.kuoxieIsLoading = true
            }, 100)
        },
        async getKuoxie() {
            const res = await this.$api.get('/ai-main-app/api/comfyPrompt/expansion', {
                params: { simplePrompt: this.value }
            })
            this.kuoxieText = res
            this.kuoxieIsLoading = false
        },
        confirm() {
            this.cancel()
        },
        delImgUrl() {
            this.imgUrl = ''
        },
        beforeUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            const isLt20M = file.size / 1024 / 1024 < 20;

            if (!isJPG && !isPNG) {
                this.$message.error('只支持上传 JPG 或 PNG 格式的文件');
                return false;
            }
            if (!isLt20M) {
                this.$message.error('单张图片不能大于20M');
                return false;
            }

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const isLt2048 = img.width <= 2048 && img.height <= 2048;
                        if (!isLt2048) {
                            this.$message.error('图片尺寸不能超过 2048x2048');
                            reject(false);
                        } else {
                            resolve(true);
                        }
                    };
                    img.onerror = () => {
                        this.$message.error('无法读取图片尺寸');
                        reject(false);
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    this.$message.error('文件读取失败');
                    reject(false);
                };
                reader.readAsDataURL(file);
            });
        },
        async httpRequest(obj, flieList) {
            const that = this;
            this.percentage = 0;

            const startTime = Date.now();

            const res = await this.$uploadBigFile(obj.file, {
                onProgress(percentage) {
                    that.percentage = percentage;
                    const currentTime = Date.now();
                    const elapsedTime = ((currentTime - startTime) / 1000).toFixed(2); // 秒
                    console.log(`上传进度: ${percentage}%`);
                    console.log(`已用时间: ${elapsedTime}秒`);
                }
            });

            this.imgUrl = res
            this.percentage = 100;

            const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
            console.log(`总上传时间: ${totalTime}秒`);

            setTimeout(() => {
                this.percentage = false;
            }, 100);


            this.getImgDesc()
        },
        async getImgDesc() {
            this.imgDesIsLoading = true
            const res = await this.$api.get('/ai-main-app/api/comfyPrompt/describeImage', {
                params: { imageUrl: this.imgUrl }
            })
            this.imgDesIsLoading = false
            this.imgDesText = res
            this.imgUrl = ''
        },
        imgDescConfirm() {
            this.$emit('input', this.imgDesText)
            this.cancel()
            setTimeout(() => {
                this.imgDesIsLoading = true
            }, 100)
        }
    }
}
</script>

<style lang="less">
.cp-cps-prompt-img-desc-mask {
    .el-upload-dragger{
        padding-top: 20px;
    }
    .upload-box {
        text-align: center;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 12px;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;

        .el-upload {


            .el-upload-dragger {
                width: 549px;
                height: 176px;
            }
        }
    }

    .desc-box {
        margin: 12px auto;
        width: 549px;

        .title {
            
            font-size: 14px;
            line-height: 20px;
            margin-bottom: 12px;
        }

        .cp-loading-text {
            height: 120px;
        }
    }

}

.cp-cps-prompt-kuoxie-mask {
    .content {
        .cp-loading-text {
            margin: 24px auto;
            width: 704px;
            height: 120px;
        }
    }
}

.cp-cps-prompt {
    .label {
        display: flex;
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .content-wrap {
        position: relative;

        .el-textarea {
            textarea {
                height: 166px;
                border-radius: 8px;
                border: 1px solid #e6e8eb; 
                padding: 12px;
                padding-bottom: 42px;
            }

            .el-input__count {  
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                bottom: 1px;
                left: 3px;
                right: 16px;
                text-align: right;
                height: 40px;
                padding-top: 9px;
                box-sizing: border-box;
                border-radius: 8px;
            }

        }

        .btns-wrap {
            position: absolute;
            left: 10px;
            bottom: 8px;
            display: flex;
            gap: 8px;
        }
    }
}
</style>