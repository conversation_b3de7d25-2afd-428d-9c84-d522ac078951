<template>
    <div class="cp-cps-image">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-upload :fileList="fileList" drag action="" :http-request="httpRequest" :limit="2" :show-file-list="false"
            :before-upload="beforeUpload">
            <div v-if="percentage !== false" class="percentage-box">
                <div class="text">图片上传中</div>
                <el-progress :percentage="percentage" :show-text="false" :strokeWidth="4"></el-progress>
            </div>
            <div v-if="value" class="preview-box">
                <el-image :src="value" fit="contain"/> 
                <i class="el-icon-close" @click.stop="delValue"></i>
            </div>
            <div v-else class="upload-box">
                <i class="iconfont icon-shangchuantupian"></i>
                <div class="el-upload__text">拖拽文件到这里或点击上传</div>
            </div>
        </el-upload>
        <div class="cp-desc">上传图片尺寸需小于2048px*2048px</div>
    </div>
</template>

<script>
export default {
    name: 'cps-IMAGE',
    props: {
        value: {},
        bind: {}
    },
    data() {
        return {
            percentage: false,
            loading: false
        }
    },
    computed: {
        fileList() {
            if (!this.value) {
                return []
            }
            return [{ url: this.value }]
        }
    },
    created() {
    },
    methods: {
        beforeUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            const isLt20M = file.size / 1024 / 1024 < 20;

            if (!isJPG && !isPNG) {
                this.$message.error('只支持上传 JPG 或 PNG 格式的文件');
                return false;
            }
            if (!isLt20M) {
                this.$message.error('单张图片不能大于20M');
                return false;
            }

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const isLt2048 = img.width <= 2048 && img.height <= 2048;
                        if (!isLt2048) {
                            this.$message.error('图片尺寸不能超过 2048x2048');
                            reject(false);
                        } else {
                            resolve(true);
                        }
                    };
                    img.onerror = () => {
                        this.$message.error('无法读取图片尺寸');
                        reject(false);
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    this.$message.error('文件读取失败');
                    reject(false);
                };
                reader.readAsDataURL(file);
            });
        },
        async httpRequest(obj, flieList) {
            const that = this;
            this.percentage = 0;

            const startTime = Date.now();

            const res = await this.$uploadBigFile(obj.file, {
                onProgress(percentage) {
                    that.percentage = percentage;
                    const currentTime = Date.now();
                    const elapsedTime = ((currentTime - startTime) / 1000).toFixed(2); // 秒
                    console.log(`上传进度: ${percentage}%`);
                    console.log(`已用时间: ${elapsedTime}秒`);
                }
            });

            this.$emit('input', res);
            this.percentage = 100;

            const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
            console.log(`总上传时间: ${totalTime}秒`);

            setTimeout(() => {
                this.percentage = false;
            }, 100);
        },
        delValue() {
            this.$emit('input', undefined)
        }
    }
}
</script>

<style lang="less">
.cp-cps-image {

    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }

    }

   
    .cp-desc {
        font-size: 14px;
        color: #9ea4b2;
        margin-top: 2px;

    }
  


}
</style>