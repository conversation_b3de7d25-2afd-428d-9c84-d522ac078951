<template>
    <div class="cp-cps-select">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-select :value="value" @input="handlerInput">
            <el-option v-for="item in bind.options" :key="item.value" :label="item.text" :value="item.value">
            </el-option>
        </el-select>
    </div>
</template>

<script>
export default {
    name: 'cps-SELECT',
    props: {
        value: {},
        bind: {}
    },
    computed: {
    },
    created() {
    },
    methods: {
        handlerInput(v) {
            this.$emit('input', v)
        }
    }
}
</script>

<style lang="less">
.cp-cps-select {

    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .el-select {
        width: 100%;

        .el-input {
            height: 33px;
            border-radius: 150px;
            border: 1px solid #e6e8eb;
            background: rgba(255, 255, 255, 0.10);
            box-sizing: border-box;
            font-size: 14px;
            width: 100%;
        }

        .el-input__inner {
            background: transparent;
            border: none !important;
            height: 100%;
            padding-left: 17px;
            height: 33px;
        }

        .el-input__suffix {
            display: flex;
            align-items: center;
            padding-right: 10px;

            i {
                font-size: 20px;
                cursor: pointer;
            }
        }
    }
}
</style>