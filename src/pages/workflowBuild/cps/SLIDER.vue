<template>
    <div class="cp-cps-slider">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-slider :value="myValue" @input="$emit('input', $event)" show-input :max="bind.ext.maxValue - 0"
            :min="bind.ext.minValue - 0" :step="bind.ext.stepValue - 0">
        </el-slider>

    </div>
</template>

<script>
export default {
    name: 'cps-SLIDER',
    props: {
        value: {},
        bind: {}
    },
    computed: {
        myValue() {
            if (this.value === undefined || this.value === '') {
                return undefined
            }
            return this.value - 0
        }
    },
    created() {
    },
    methods: {
    }
}
</script>

<style lang="less">
.cp-cps-slider {

    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .el-slider {
        margin-top: -6px;

        
    }
}
</style>