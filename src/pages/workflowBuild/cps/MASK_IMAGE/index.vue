<template>
    <div class="cp-cps-image-mask">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-upload :fileList="fileList" drag action="" :http-request="httpRequest" :limit="2" :show-file-list="false"
            :before-upload="beforeUpload">
            <div v-if="percentage !== false" class="percentage-box">
                <div class="text">图片上传中</div>
                <el-progress :percentage="percentage" :show-text="false" :strokeWidth="4"></el-progress>
            </div>
            <div v-if="value" class="preview-box">
                <div class="image-wrap">
                    <img :src="myValue.url" fit="contain" />
                    <img v-if="myValue.mask" class="mask" :src="myValue.mask" fit="contain" />
                </div>
                <i class="el-icon-close" @click.stop="delValue"></i>
                <div class="btn3" @click.stop="openCanavs">
                    <i class="iconfont icon-huizhimengban"></i>
                    <span>绘制蒙版</span>
                </div>
            </div>
            <div v-else class="upload-box">
                <i class="iconfont icon-shangchuantupian"></i>
                <div class="el-upload__text">拖拽文件到这里或点击上传</div>
            </div>
        </el-upload>
        <div class="cp-desc">上传图片尺寸需小于2048px*2048px</div>
        <el-dialog title="绘制蒙版（只需绘制需要修改的地方）" :visible.sync="dialogVisible" custom-class="cp-cps-image-mask"
            width="842px" top="0">
            <div class="cp-cps-image-mask-canvas-wrap">
                <cpCanvas v-if="dialogVisible" ref="canvas" :bkSrc="myValue.url" :brushSize="brushSize"
                    :opacity="opacity" :maskSrc="myValue.mask" @wheel.native="handleWheelScroll" />
            </div>
            <div slot="footer" class="dialog-footer-between">
                <div class="btn-left">
                    <div class="btn2 small" @click="clear"><i class="iconfont icon-qingchu"></i>清除</div>
                    <div class="btn-opt">
                        <span>画笔大小</span>
                        <el-slider v-model="brushSize" :max="maxBrushSize" :min="minBrushSize"></el-slider>
                    </div>
                    <div class="btn-opt">
                        <span>不透明度</span>
                        <el-slider v-model="opacity" :max="1" :min="0" :step="0.1"></el-slider>
                    </div>
                </div>
                <div class="btn-right">
                    <div class="btn2 small" @click="cancel">取消</div>
                    <div class="btn1 small" @click="confirm">确定</div>
                </div>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import cpCanvas from './canvas.vue'
export default {
    name: 'cps-MASK_IMAGE',
    components: {
        cpCanvas
    },
    props: {
        value: {},
        bind: {}
    },
    data() {
        return {
            dialogVisible: false,
            percentage: false,
            loading: false,
            brushSize: 10,
            opacity: 0.6,
            minBrushSize: 1,
            maxBrushSize: 60,
        }
    },
    computed: {
        fileList() {
            if (!this.value) {
                return []
            }
            return [{ url: this.myValue.url }]
        },
        myValue() {
            if (!this.value) {
                return {}
            }
            const arr = this.value.split(',')
            return {
                url: arr[0],
                dataMask: arr[1],
                mask: arr[2],
            }
        }
    },
    created() {
    },
    methods: {
        handleWheelScroll(event) {
            if (event.deltaY < 0) {
                // 向上滚动
                if (this.brushSize < this.maxBrushSize) {
                    this.brushSize++;
                }
            } else {
                // 向下滚动
                if (this.brushSize > this.minBrushSize) {
                    this.brushSize--;
                }
            }
        },
        setValue({ url, mask, dataMask }) {
            const valueArr = [url ?? this.myValue.url, dataMask ?? this.myValue.dataMask, mask ?? this.myValue.mask]
            this.$emit('input', valueArr.join(','))
        },
        openCanavs() {
            this.dialogVisible = true
        },
        beforeUpload(file) {
            const isJPG = file.type === 'image/jpeg';
            const isPNG = file.type === 'image/png';
            const isLt20M = file.size / 1024 / 1024 < 20;

            if (!isJPG && !isPNG) {
                this.$message.error('只支持上传 JPG 或 PNG 格式的文件');
                return false;
            }
            if (!isLt20M) {
                this.$message.error('单张图片不能大于20M');
                return false;
            }

            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const isLt2048 = img.width <= 2048 && img.height <= 2048;
                        if (!isLt2048) {
                            this.$message.error('图片尺寸不能超过 2048x2048');
                            reject(false);
                        } else {
                            resolve(true);
                        }
                    };
                    img.onerror = () => {
                        this.$message.error('无法读取图片尺寸');
                        reject(false);
                    };
                    img.src = e.target.result;
                };
                reader.onerror = () => {
                    this.$message.error('文件读取失败');
                    reject(false);
                };
                reader.readAsDataURL(file);
            });
        },
        async httpRequest(obj, flieList) {
            const that = this;
            this.percentage = 0;

            const startTime = Date.now();

            const res = await this.$uploadBigFile(obj.file, {
                onProgress(percentage) {
                    that.percentage = percentage;
                    const currentTime = Date.now();
                    const elapsedTime = ((currentTime - startTime) / 1000).toFixed(2); // 秒
                    console.log(`上传进度: ${percentage}%`);
                    console.log(`已用时间: ${elapsedTime}秒`);
                }
            });

            this.setValue({ url: res });
            this.percentage = 100;

            const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
            console.log(`总上传时间: ${totalTime}秒`);

            setTimeout(() => {
                this.percentage = false;
                this.setValue({ mask: '', dataMask: '' });
                this.openCanavs()
            }, 100);
        },
        delValue() {
            this.$emit('input', undefined)
        },
        cancel() {
            this.dialogVisible = false
        },
        async confirm() {
            const { mask, dataMask } = await this.$refs.canvas.uploadCanvasImage()
            this.setValue({ mask: `${mask}?t=${Date.now()}`, dataMask: `${dataMask}?t=${Date.now()}` })
            this.dialogVisible = false
        },
        clear() {
            this.$refs.canvas.clear()
        }
    }
}
</script>

<style lang="less">
.cp-cps-image-mask {
    .preview-box {

        .image-wrap {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            img {
                position: absolute;
                max-width: 100%;
                max-height: 100%;
                
                &.mask {
                    opacity: 0.5;
                }
            }
        }

    }


    .cp-cps-image-mask-canvas-wrap {
        width: 840px;
        height: 522px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
    }


    .btn-opt {
        .el-slider {
            width: 150px;
            margin-left: 10px;
            margin-right: 8px;
        }
    }

    .el-dialog__footer {
        .btn2 {
            i {
                font-size: 16px;
                margin-right: 4px;
                line-height: 32px;
                position: relative;
                top: 1px;
            }
        }
    }
}

.cp-cps-image-mask {

    .label {
        display: flex;

        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }

    }



    .cp-desc {
        font-size: 14px;
        color: #9ea4b2;
        margin-top: 2px;

    }
}
</style>