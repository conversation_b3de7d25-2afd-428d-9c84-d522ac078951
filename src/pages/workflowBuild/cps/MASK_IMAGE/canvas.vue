<template>
    <div ref="canvasContainer" class="cp-canvas" @mouseenter="showCustomCursor" @mouseleave="hideCustomCursor"
        @mousemove="moveCustomCursor">
        <div class="img-wrap"
            :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px', backgroundImage: `url(${bkSrc})` }"></div>
        <canvas :style="{ opacity }" ref="canvas" :width="canvasWidth" :height="canvasHeight"
            @mousemove="draw"></canvas>
        <div v-show="isCursorVisible" :style="customCursorStyle" class="custom-cursor"></div>
    </div>
</template>
<script>
export default {
    name: 'cpCanvas',
    props: {
        maxWidth: {
            type: Number,
            default: 840
        },
        maxHeight: {
            type: Number,
            default: 520
        },
        bkSrc: {
            type: String,
            required: true
        },
        brushSize: {
            type: Number,
            default: 5
        },
        maskSrc: {
            type: String
        },
        brushColor: {
            type: String,
            default: '#000' // 默认半透明黑色
        },
        opacity: {
            type: Number,
            default: 0.5
        }
    },
    data() {
        return {
            isDrawing: false,
            canvasWidth: 0,
            canvasHeight: 0,
            imgWidth: 0,
            imgHeight: 0,
            lastX: 0,
            lastY: 0,
            isCursorVisible: false,
            cursorX: 0,
            cursorY: 0,
        };
    },
    computed: {
        customCursorStyle() { 
            return {
                position: 'fixed',
                top: `${this.cursorY-this.brushSize/2  }px`,
                left: `${this.cursorX-this.brushSize/2  }px`,
                width: `${this.brushSize}px`,
                height: `${this.brushSize}px`,
                border: '1px solid #fff',
                borderRadius: '50%',
                pointerEvents: 'none',
                boxSizing:'border-box'
            };
        },
    },
    async mounted() {
        await this.setCanvasSize()
        const canvas = this.$refs.canvas;
        this.ctx = canvas.getContext('2d');
        this.setMaskImage()
        // 添加全局事件监听器
        window.addEventListener('mousedown', this.startDrawing);
        window.addEventListener('mouseup', this.stopDrawing);
    },
    beforeDestroy() {
        // 移除全局事件监听器
        window.removeEventListener('mousedown', this.startDrawing);
        window.removeEventListener('mouseup', this.stopDrawing);
    },
    methods: {
        showCustomCursor() {
            this.isCursorVisible = true;
        },
        hideCustomCursor() {
            this.isCursorVisible = false;
        },
        moveCustomCursor(event) {
            this.cursorX = event.clientX 
            this.cursorY = event.clientY 
        },
        setMaskImage() {
            if (!this.maskSrc) return;
            const img = new Image();
            img.onload = () => {
                const canvas = this.$refs.canvas
                this.ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            };
            img.crossOrigin = 'anonymous'
            img.src = this.maskSrc;
        },
        setCanvasSize() {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => {
                    this.imgWidth = img.width;
                    this.imgHeight = img.height;
                    const aspectRatio = img.width / img.height;
                    const maxAspectRatio = this.maxWidth / this.maxHeight;
                    if (img.width > this.maxWidth || img.height > this.maxHeight) {
                        if (aspectRatio > maxAspectRatio) {
                            // 宽图
                            this.canvasWidth = this.maxWidth;
                            this.canvasHeight = Math.floor(this.maxWidth / aspectRatio);
                        } else {
                            // 高图
                            this.canvasHeight = this.maxHeight;
                            this.canvasWidth = Math.floor(this.maxHeight * aspectRatio);
                        }
                    } else {
                        // 原图尺寸在限制范围内
                        this.canvasWidth = img.width;
                        this.canvasHeight = img.height;
                    }
                    resolve();
                };
                img.src = this.bkSrc;
            });
        },
        clear() {
            const canvas = this.$refs.canvas;
            const ctx = this.ctx;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        },
        // 上传cavas图片
        async uploadCanvasImage() {
            const maskFile = await this.processAndScaleCanvas(false);
            const res1 = await this.$uploadBigFile(maskFile, {}); 

            const maskDataFile = await this.processAndScaleCanvas(true);
            const res2 = await this.$uploadBigFile(maskDataFile, {}); 

            return {
                mask: res1,
                dataMask: res2
            };
        },
        copyImageData(imageData) {
            const newImageData = new ImageData(imageData.width, imageData.height);
            const data = imageData.data;
            const newData = newImageData.data;
            for (let i = 0; i < data.length; i++) {
                newData[i] = data[i];
            }
            return newImageData;
        },
        processAndScaleCanvas(changeColor) {
            const canvas = this.$refs.canvas;
            const ctx = canvas.getContext('2d');
            const imageData = this.copyImageData(ctx.getImageData(0, 0, canvas.width, canvas.height));
            const data = imageData.data;

            // 修改像素数据
            if (changeColor) {
                for (let i = 0; i < data.length; i += 4) {
                    const alpha = data[i + 3];
                    if (alpha === 0) {
                        // 透明像素变为黑色
                        data[i] = 0;     // Red
                        data[i + 1] = 0; // Green
                        data[i + 2] = 0; // Blue
                        data[i + 3] = 255; // Alpha
                    } else {
                        // 其他像素变为白色
                        data[i] = 255;     // Red
                        data[i + 1] = 255; // Green
                        data[i + 2] = 255; // Blue
                        data[i + 3] = 255; // Alpha
                    }
                }
            }

            // 创建一个新的 canvas 用于放大
            const scaledCanvas = document.createElement('canvas');
            scaledCanvas.width = this.imgWidth;
            scaledCanvas.height = this.imgHeight;
            const scaledCtx = scaledCanvas.getContext('2d');

            // 将原始 canvas 的内容绘制到新的放大 canvas 上
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.putImageData(imageData, 0, 0);

            // 使用 drawImage 方法放大内容
            scaledCtx.drawImage(tempCanvas, 0, 0, canvas.width, canvas.height, 0, 0, scaledCanvas.width, scaledCanvas.height);

            // 将放大后的 canvas 内容转换为 Blob 对象并返回 Blob URL
            return new Promise((resolve) => {
                scaledCanvas.toBlob((blob) => {
                    const file = new File([blob], `scaled-image-${Date.now()}.png`, { type: 'image/png' });
                    resolve(file);
                }, 'image/png');
            });
        },
        startDrawing(event) {
            if (event.target !== this.$refs.canvas || event.button !== 0) return; // 确保只在画布上绘制
            this.isDrawing = true;
            const { x, y } = this.getMousePos(event);
            this.lastX = x;
            this.lastY = y;
            this.draw(event)
        },
        draw(event) {
            if (!this.isDrawing) return;
            const { x, y } = this.getMousePos(event);
            const ctx = this.ctx;
            ctx.globalAlpha = 1;
            ctx.strokeStyle = this.brushColor;
            ctx.lineWidth = this.brushSize;
            ctx.lineCap = 'round';
            ctx.beginPath();
            ctx.moveTo(this.lastX, this.lastY);
            ctx.lineTo(x, y);
            ctx.stroke();
            this.lastX = x;
            this.lastY = y;
        },
        stopDrawing() {
            this.isDrawing = false;
        },
        clearCanvas() {
            const canvas = this.$refs.canvas;
            const ctx = this.ctx;
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        },
        getMousePos(event) {
            const rect = this.$refs.canvas.getBoundingClientRect();
            return {
                x: event.clientX - rect.left,
                y: event.clientY - rect.top
            };
        }
    }
};
</script>
<style scoped>
.cp-canvas {
    user-select: none;
    position: relative;
    display: inline-block;
    font-size: 0;
    cursor: none;
}

.img-wrap {
    pointer-events: none;
    background-size: 100% 100%;
}

canvas {
    position: absolute;
    top: 0;
    left: 0;
}

.cursor {
    position: absolute;
}
</style>