<template>
    <div class="cp-cps-seed">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-input :value="value" @input="$emit('input', $event)" placeholder="不填写为随机">
            <i slot="suffix" v-popover:popover class="iconfont icon-sheweisuiji1" @click="generateRandomNumber"></i>
        </el-input>
        <el-popover ref="popover" placement="top" width="auto" trigger="hover" content="随机"></el-popover>
    </div>
</template>

<script>
export default {
    name: 'cps-SEED',
    props: {
        value: {},
        bind: {}
    },
    computed: {
    },
    created() {
    },
    methods: {
        generateRandomNumber() {
            let randomNumber = '';
            for (let i = 0; i < 15; i++) {
                randomNumber += Math.floor(Math.random() * 10);
            }
            this.$emit('input', randomNumber)
        }
    }
}
</script>

<style lang="less">
.cp-cps-seed {
    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        line-height: 1;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .el-input {
        height: 33px;
        border-radius: 150px;
        border: 1px solid #e6e8eb;
        background: rgba(255, 255, 255, 0.10);
        box-sizing: border-box;
        font-size: 14px;

        .el-input__inner {
            background: transparent;
            border: none;
            height: 100%;
            padding-left: 17px;
        }

        .el-input__suffix {
            display: flex;
            align-items: center;
            padding-right: 10px;

            i {
                font-size: 20px;
                cursor: pointer;
            }
        }
    }
}
</style>