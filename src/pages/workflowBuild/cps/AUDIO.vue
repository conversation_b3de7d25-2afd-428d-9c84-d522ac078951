<template>
    <div class="cp-cps-audio">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-upload :file-list="fileList" drag action="" :http-request="httpRequest" :limit="1" :show-file-list="false"
            :before-upload="beforeUpload">
            <div v-if="percentage !== false" class="percentage-box">
                <div class="text">音频上传中</div>
                <el-progress :percentage="percentage" :show-text="false" :stroke-width="4"></el-progress>
            </div>
            <div v-if="value" class="preview-box">
                <div class="audio-preview">
                    <audio controls>
                        <source :src="value" type="audio/mpeg">
                        您的浏览器不支持音频播放
                    </audio>
                    <!-- <div class="file-name">{{ fileName }}</div> -->
                </div>
                <i class="el-icon-close" @click.stop="delValue"></i>
            </div>
            <div v-else class="upload-box">
                <i class="iconfont icon-music"></i>
                <div class="el-upload__text">拖拽音频文件到这里或点击上传</div>
            </div>
        </el-upload>
        <div class="cp-desc">支持格式：MP3/WAV，文件大小需小于20MB</div>
    </div>
</template>

<script>
export default {
    name: 'cps-AUDIO',
    props: {
        value: String,
        bind: Object
    },
    data() {
        return {
            percentage: false,
            fileName: ''
        };
    },
    computed: {
        fileList() {
            return this.value ? [{ name: this.fileName }] : [];
        }
    },
    methods: {
        beforeUpload(file) {
            const validTypes = ['audio/mpeg', 'audio/wav', 'audio/x-wav'];
            const isValidType = validTypes.includes(file.type);
            const isLt20M = file.size / 1024 / 1024 < 20;

            if (!isValidType) {
                this.$message.error('只支持上传 MP3/WAV 格式的音频文件');
                return false;
            }

            if (!isLt20M) {
                this.$message.error('音频文件大小不能超过20MB');
                return false;
            }

            this.fileName = file.name;
            return true;
        },

        async httpRequest({ file }) {
            this.percentage = 0;
            const startTime = Date.now();

            try {
                const res = await this.$uploadBigFile(file, {
                    onProgress: (percentage) => {
                        this.percentage = percentage;
                        const elapsed = ((Date.now() - startTime) / 1000).toFixed(2);
                        console.log(`上传进度: ${percentage}%，已用时间: ${elapsed}秒`);
                    }
                });

                this.$emit('input', res);
                this.percentage = 100;

                const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
                console.log(`总上传时间: ${totalTime}秒`);
            } finally {
                setTimeout(() => {
                    this.percentage = false;
                }, 1000);
            }
        },

        delValue() {
            this.$emit('input', '');
            this.fileName = '';
        }
    }
};
</script>

<style lang="less">
.cp-cps-audio {
    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .cp-desc {
        font-size: 12px;
        color: #9ea4b2;
        margin-top: 8px;
    }
 

    .preview-box {
        position: relative;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;

        .audio-preview {
            display: flex;
            flex-direction: column;
            align-items: center;

            audio {
                width: 240px; 
            }

            .file-name {
                font-size: 12px;
                color: #666;
                max-width: 240px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .el-icon-close {
            position: absolute;
            top: 5px;
            right: 5px;
            cursor: pointer;
            
        }
    }

    .upload-box {
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
            font-size: 32px;
            color: #409EFF;
            margin-bottom: 8px;
        }

        .el-upload__text {
            color: #606266;
        }
    }
}
</style>