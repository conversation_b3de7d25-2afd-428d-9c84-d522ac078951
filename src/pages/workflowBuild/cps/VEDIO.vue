<template>
    <div class="cp-cps-video">
        <div class="label">
            <div class="text">{{ bind.keyName }}</div>
            <cp-tips v-if="bind.tips" :text="bind.tips" />
        </div>
        <el-upload :file-list="fileList" drag action="" :http-request="httpRequest" :limit="1" :show-file-list="false"
            :before-upload="beforeUpload">
            <div v-if="percentage !== false" class="percentage-box">
                <div class="text">视频上传中</div>
                <el-progress :percentage="percentage" :show-text="false" :stroke-width="4"></el-progress>
            </div>
            <div v-if="value" class="preview-box">
                <div class="video-preview">
                    <video controls>
                        <source :src="value" type="video/mp4">
                        您的浏览器不支持视频播放
                    </video>
                    <!-- <div class="file-name">{{ fileName }}</div> -->
                </div>
                <i class="el-icon-close" @click.stop="delValue"></i>
            </div>
            <div v-else class="upload-box">
                <i class="iconfont icon-video"></i>
                <div class="el-upload__text">拖拽视频文件到这里或点击上传</div>
            </div>
        </el-upload>
        <div class="cp-desc">支持格式：MP4/AVI/MOV，文件大小需小于100MB</div>
    </div>
</template>

<script>
export default {
    name: 'cps-VIDEO',
    props: {
        value: String,
        bind: Object
    },
    data() {
        return {
            percentage: false,
            fileName: ''
        };
    },
    computed: {
        fileList() {
            return this.value ? [{ name: this.fileName }] : [];
        }
    },
    methods: {
        beforeUpload(file) {
            const validTypes = ['video/mp4', 'video/x-msvideo', 'video/quicktime'];
            const isValidType = validTypes.includes(file.type);
            const isLt100M = file.size / 1024 / 1024 < 100;

            if (!isValidType) {
                this.$message.error('只支持上传 MP4/AVI/MOV 格式的视频文件');
                return false;
            }

            if (!isLt100M) {
                this.$message.error('视频文件大小不能超过100MB');
                return false;
            }

            this.fileName = file.name;
            return true;
        },

        async httpRequest({ file }) {
            this.percentage = 0;
            const startTime = Date.now();

            try {
                const res = await this.$uploadBigFile(file, {
                    onProgress: (percentage) => {
                        this.percentage = percentage;
                        const elapsed = ((Date.now() - startTime) / 1000).toFixed(2);
                        console.log(`上传进度: ${percentage}%，已用时间: ${elapsed}秒`);
                    }
                });

                this.$emit('input', res);
                this.percentage = 100;

                const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
                console.log(`总上传时间: ${totalTime}秒`);
            } finally {
                setTimeout(() => {
                    this.percentage = false;
                }, 1000);
            }
        },

        delValue() {
            this.$emit('input', '');
            this.fileName = '';
        }
    }
};
</script>

<style lang="less">
.cp-cps-video {
    .label {
        display: flex;
        
        font-size: 14px;
        align-items: center;
        margin-bottom: 12px;

        i {
            margin-left: 4px;
        }
    }

    .cp-desc {
        font-size: 12px;
        color: #9ea4b2;
        margin-top: 8px;
    }
 
    .preview-box {
        position: relative;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;

        .video-preview {
            display: flex;
            flex-direction: column;
            align-items: center;

            video {
                width: 240px;
                max-height: 180px;
            }

            .file-name {
                font-size: 12px;
                color: #666;
                max-width: 240px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }

        .el-icon-close {
            position: absolute;
            top: 5px;
            right: 5px;
            cursor: pointer;
            
        }
    }

    .upload-box {
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
            font-size: 32px;
            color: #409EFF;
            margin-bottom: 8px;
        }

        .el-upload__text {
            color: #606266;
        }
    }
}
</style>