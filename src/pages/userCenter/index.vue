<template>
  <div class="page-usercenter hover-scroll">
    <div class="banner">
      <div class="userinfo">
        <img class="logo" :src="userInfo.avatarUrl" alt="" srcset="">
        <div class="row">
          <div class="col1">{{ userInfo.name }}</div>
          <div class="col2">{{ userInfo.position }}</div>
        </div>
      </div>
      <img class="img-wenzi" src="../../assets/banner-wenzhi.png" alt="" srcset="">

    </div>

    <div class="tabs">
      <el-tabs v-model="activeName">
        <el-tab-pane label="我的创作" name="myCreation"></el-tab-pane>
      </el-tabs>
    </div>
    <workflowWaterfall ref="workflow" :getApiData="getApiData" emptyText="暂无作品，快去创作AI绘图作品吧～"></workflowWaterfall>
  </div>
</template>
<script>
import { mapState } from 'vuex'

import workflowWaterfall from '@/pages/sourceMaterial/workflow-waterfall'

export default {
  components: {
    workflowWaterfall
  },
  data() {
    return {
      activeName: 'myCreation'
    };
  },
  watch: {

  },
  computed: {
    ...mapState(['userInfo'])
  },
  async created() {
  },
  beforeDestroy() {
  },
  methods: {
    async getApiData({ pageNo, pageSize }) {
      return await this.$api.post('/ai-main-app/api/comfyWorkflowLog/list', {
        data: {
          statuses: ['COMPLETED']
        },
        "orderBy": "id",
        pageNo,
        pageSize
      })
    }
  },
  mounted() {
  }
};
</script>

<style lang="less">
.page-usercenter {
  padding: 0px 62px;
  height: 100%;
  box-sizing: border-box;
  overflow: auto;

  .banner {
    width: auto;
    height: 120px;
    background: url(../../assets/banner-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    border-radius: 8px;

    .img-wenzi {
      height: 80px;
      position: absolute;
      right: 48px;
      top: 22px;
    }

    .userinfo {
      position: absolute;
      left: 48px;
      top: -26px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: flex-start;

      .logo {
        width: 80px;
        height: 80px;
        border-radius: 1230px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .row {
        font-size: 16px;
        display: flex;
        align-items: baseline;
        gap: 12px;

        .col1 {
          font-weight: bold;
          margin-bottom: 2px;
          font-size: 22px;
        }

        .col2 {
          font-size: 14px;
        }
      }
    }
  }



  .tabs {
    padding: 16px 0 0;
  }


  .cp-workflow-waterfall {
    height: calc(100% - 226px);
  }
}
</style>