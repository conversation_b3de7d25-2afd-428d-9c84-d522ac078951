<template>
  <div
    class="ai-skills-container"
    v-loading="loading"
    element-loading-spinner="el-loading-spinne"
    @scroll="handleScroll"
  >
    <div class="blank_div"></div>
    <!-- 顶部标题和搜索区域 -->
    <div class="header-section">
      <div class="title">AI技能库</div>
      <div class="search-input">
        <el-input
          placeholder="请输入内容"
          v-model="showNameLike"
          @keyup.enter.native="search"
        >
          <el-button
            slot="append"
            type="primary"
            icon="el-icon-search"
            @click="search"
            >搜索</el-button
          >
        </el-input>
      </div>
    </div>
    <!-- 并列筛选条件 -->
    <div class="filter-section sticky">
      <div
        v-for="category in skillCategories"
        :key="category.id"
        class="filter-group"
      >
        <span class="filter-label">{{ category.name }}：</span>
        <div class="filter-options" v-if="category.id !== 'position'">
          <el-tag
            v-for="option in category.options"
            :key="option.id"
            :type="getTagType(category.id, option.label)"
            class="filter-tag"
            @click="handleTagClick(category.id, option.label)"
          >
            {{ option.label }}
          </el-tag>
        </div>
        <div v-else>
          <el-select
            size="mini"
            filterable
            clearable
            v-model="selectedFilters.position"
            placeholder="选择岗位"
            class="filter-select"
            @change="search"
          >
            <el-option
              v-for="option in category.options"
              :key="option.id"
              :label="option.label"
              :value="option.label"
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <!-- 技能展示区域 -->
    <div class="skill-list-wrap">
      <el-row
      :gutter="20"
      class="skill-list"
      v-if="skills.length > 0"
      v-loading="listLoading"
      element-loading-spinner="el-loading-spinne"
      :style="{ height: tableHeight }"
    >
      <el-col
        v-for="skill in skills"
        :key="skill.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <el-card class="skill-card" @click.native="jump(skill)">
          <div class="skill-type">{{ skill.showType }}</div>
          <div class="skill-top">
            <div class="skill-name">
              <img class="skill-img" :src="skill.iconUrl" alt="" /><span>{{
                skill.showName
              }}</span>
            </div>
          </div>
          <div class="skill-content">
            {{ skill.description }}
          </div>
          <i class="share-btn iconfont icon-fenxiang" @click.stop="shareSkill(skill)" title="技能分享"></i>
        </el-card>
      </el-col>
    </el-row>
    </div>
    <!-- 暂无数据展示 -->
    <div class="no-data" v-if="skills.length === 0 && !isDataLoading">
      <i class="iconfont icon--zwsj"></i>
      <p class="no-data-text">暂无数据</p>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import skill from "../../mixins/skill";
import utils from "@/utils/index.js";
export default {
  mixins: [skill],
  data() {
    return {
      listLoading: true,
      loading: true,
      showNameLike: "",
      selectedFilters: {
        skill_category: "", // 改为单选
        position: "", // 改为单选
        skill_type: "", // 改为单选
      },
      tableHeight: 0,
      currentPage: 1,
      pageSize: 30,
      total: 0,
      skillCategories: [], // 初始化为空数组
      skills: [],
      isDataLoading: false,
    };
  },
  async mounted() {
    this.loading = true;
    await this.getFunctionOptions();
    await this.getTypeOptions();
    await this.getDomainOptions();
    await this.getSkills();
    this.loading = false;
    // 获取容器的引用并添加滚动事件监听
    const container = document.querySelector(".right-content");
    const sikllList = document.querySelector(".skill-list");
    if (container) {
      const height = container.clientHeight;
      this.tableHeight = `${height - 50}px`;
    }
    // 设定滚动区域的高度
    window.addEventListener("resize", this.setTableHeight);
    sikllList.addEventListener("scroll", this.handleScroll);
  },
  beforeDestroy() {
    // 移除滚动事件监听
    const sikllList = document.querySelector(".skill-list");
    if (sikllList) {
      sikllList.removeEventListener("scroll", this.handleScroll);
    }
    window.removeEventListener("resize", this.setTableHeight);
  },
  computed: {
    ...mapState(["isNavCollapsed"]), // 使用 Vuex 的状态
  },
  methods: {
    search() {
      this.currentPage = 1; // 重置页码
      this.skills = []; // 清空当前技能列表
      this.getSkills();
    },
    async getSkills() {
      this.listLoading = true;
      this.isDataLoading = true;

      const currentFilters = { ...this.selectedFilters };

      const params = {
        data: {
          domainNames: currentFilters.position ? [currentFilters.position] : [],
          showNameLike: this.showNameLike,
          sceneCategories: currentFilters.skill_category
            ? [currentFilters.skill_category]
            : [],
          showTypes: currentFilters.skill_type
            ? [currentFilters.skill_type]
            : [],
        },
        pageNo: this.currentPage,
        pageSize: this.pageSize,
      };

      try {
        const res = await this.$api.post(
          "/ai-main-app/api/conversationSkill/listPage",
          params
        );
        if (
          JSON.stringify(currentFilters) ===
          JSON.stringify(this.selectedFilters)
        ) {
          this.skills = [...this.skills, ...res.list];
          this.total = res.total;
        }
      } catch (error) {
        console.error("Error fetching skills:", error);
      } finally {
        this.listLoading = false;
        this.isDataLoading = false;
      }
    },
    async getFunctionOptions() {
      try {
        const res = await this.$api.post(
          "/common/api/dict/listByTypeOrderBySort",
          ["skill_category"]
        );
        this.skillCategories.push({
          id: "skill_category",
          name: "场景分类",
          options: [
            { id: "all", label: "全部" }, // 添加全部选项
            ...res.skill_category.map((item) => ({
              id: item.id,
              label: item.label,
            })),
          ],
        });
      } catch (error) {
        console.error("Error fetching function options:", error);
      }
    },
    async getDomainOptions() {
      try {
        const res = await this.$api.get(
          "/ai-main-app/api/conversationSkill/listAllDomain"
        );
        this.skillCategories.push({
          id: "position",
          name: "关联岗位",
          options: [...res.map((item) => ({ id: item, label: item }))],
        });
      } catch (error) {
        console.error("Error fetching domain options:", error);
      }
    },
    async getTypeOptions() {
      try {
        const res = await this.$api.post(
          "/common/api/dict/listByTypeOrderBySort",
          ["skill_type"]
        );
        this.skillCategories.push({
          id: "skill_type",
          name: "技能类型",
          options: [
            { id: "all", label: "全部" }, // 添加全部选项
            ...res.skill_type.map((item) => ({
              id: item.id,
              label: item.label,
            })),
          ],
        });
      } catch (error) {
        console.error("Error fetching type options:", error);
      }
    },
    handleTagClick(category, option) {
      this.selectedFilters[category] = option === "全部" ? "" : option;
      this.search(); // 调用搜索方法刷新数据
    },
    getTagType(category, option) {
      return this.selectedFilters[category] === option ? "success" : "info";
    },
    handleScroll: utils.debounce(function (event) {
      const container = event.target;
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight - 300
      ) {
        if (this.skills.length < this.total) {
          this.currentPage++;
          this.getSkills();
        }
      }
    }, 300),
  },
};
</script>
<style lang="less">
.ai-skills-container {
  padding: 0 8vw;
  padding-top: 0 !important;
  margin: 0 auto;
  position: relative;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto; // 允许垂直滚动
  .blank_div{
    width: 100%;
    height: 51px;
  }
  .header-section {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
    .title {
      font-size: 22px;
      font-weight: bold;
      line-height: normal;
      margin-bottom: 20px;
      text-align: center;
    }
    .search-input {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      // margin-bottom: 32px;
      .el-input {
        width: 400px;
        input.el-input__inner {
          color: #000;
          border-radius: 1000px;
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
          border-color: #00b55b;
        }
        .el-input-group__append {
          border-color: #00b55b;
          border-radius: 0;
          border-top-right-radius: 1000px;
          border-bottom-right-radius: 1000px;
          overflow: hidden;
          background-color: #00b55b;
          .el-button {
            border: 0;
            height: auto;
          }
        }
        .el-button {
          background-color: #00b55b;
          color: #fff;
        }
      }
      .btn1 {
        margin: 0;
        width: 100px;
        height: 32px;
      }
    }
  }
  .filter-section {
    margin-bottom: 10px;
    padding-bottom: 10px;
    padding-top: 22px;
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 100; /* 增加 z-index 确保其在其他内容之上 */
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* 添加阴影以增强视觉效果 */
    .filter-group {
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      .filter-label {
        color: #6e768c;
        font-size: 14px;
        min-width: 60px;
      }
      .filter-options {
        flex: 1;
        display: flex;
        gap: 8px 12px;
      }
      .filter-tag {
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        height: 26px;
        line-height: 26px;
        border-radius: 4px;
        &:hover {
          background-color: rgba(0, 0, 0, 0);
          color: #00b55b;
          border-color: rgba(0, 0, 0, 0);
        }
      }
      .filter-select {
        width: 100%;
      }
    }
  }
  .skill-list-wrap{
    // padding-bottom: 30px;
    overflow: hidden;
  }
  .skill-list {
  margin: 20px 0;
  margin-top: 0;
  box-sizing: border-box;
  height: auto !important;
  .el-col {
    height: 110px;
    margin-bottom: 20px;
  }
  .skill-card {
    transition: all 0.3s;
    border-radius: 8px;
    border: 1px solid #eee;
    cursor: pointer;
    height: 108px;
    position: relative;
    .skill-type {
      align-items: center;
      background: linear-gradient(
        270deg,
        rgba(198, 240, 230, 0.16),
        rgba(198, 240, 230, 0.4)
      );
      border-radius: 0;
      border-bottom-left-radius: 10px;
      color: #25ab46;
      display: flex;
      justify-content: center;
      padding: 3px 8px;
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
    }
    .skill-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      .skill-name {
        flex: 2;
        font-weight: 500;
        display: flex;
        align-items: center;
        color: #333;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .skill-name span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .skill-img {
        width: 24px;
        height: 24px;
        margin-right: 10px;
      }
    }
    .skill-content {
      flex: 1;
      font-size: 12px;
      color: #666;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    &:hover {
      border-color: #00b259;
    }
    ::v-deep .el-card__body {
      padding: 15px;
    }

    .share-btn{
      font-size: 14px;
      position: absolute;
      right: 8px;
      bottom: 8px;
      color: #6E768C;
    }
  }
  .bottom-spacing {
    height: 50px; /* 设置底部占位元素的高度 */
  }
}
  /deep/.el-tag.el-tag--success {
    background: #00743a;
    color: #ffffff;
  }
  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 100px;
    color: #9ea4b2;
    border-radius: 8px;
    .iconfont {
      font-size: 30px;
    }
    .no-data-text {
      font-size: 14px;
      color: #909399;
    }
  }
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected {
  color: #00b259 !important;
}
::v-deep .el-loading-spinner .circular {
  stroke: #00b259 !important;
}
.el-tag.el-tag--info {
  background-color: rgba(0, 0, 0, 0) !important;
  border-color: rgba(0, 0, 0, 0) !important;
  color: #6e768c;
}
input.el-input__inner:focus {
  border-color: #00b259 !important;
}
.el-select .el-input.is-focus .el-input__inner {
  border-color: #00b259 !important;
  color: #00b259 !important;
}
.el-input--mini .el-input__inner {
  color: #00b259 !important;
}
.el-tag.el-tag--success {
  color: #ffffff !important;
  background-color: #00b55b !important;
}
.el-card.is-always-shadow{
  box-shadow: none !important;
}
</style>