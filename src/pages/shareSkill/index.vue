<template>
    <div class="empty-container">
        <el-empty v-if="!hasSkill" description="暂无该技能权限">
            <template #image>
                <i class="iconfont icon-zanwuquanxian"></i>
            </template>
            <el-button type="primary" @click="goBack">返回对话</el-button>
        </el-empty>
    </div>
</template>


<script>
import skill from '@/mixins/skill.js'
export default {
    mixins: [skill],
    data() {
        return {
            hasSkill: true
        }
    },
    created() {
        this.goSkill()
    },
    methods: {
        async goSkill() {
            const id = this.$route.query.id
            const res = await this.$api.get('/ai-main-app/api/conversationSkill/get', { params: { id } })
            if (res) {
                this.hasSkill = true
                this.jump(res)
            } else {
                this.hasSkill = false
            }
        },
        goBack() {
            this.$router.push('/')
        }
    }
}
</script>

<style scoped>
.empty-container {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: -60px;

    /deep/ {
        .iconfont {
            font-size: 120px;
            color: #909399;
        }
    }
}
</style>