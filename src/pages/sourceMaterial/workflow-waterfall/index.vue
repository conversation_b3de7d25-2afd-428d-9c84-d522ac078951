<template>
    <div class="cp-workflow-waterfall">
        <cp-empty :text="emptyText" v-if="!list.length && !dataLoading" />
        <vue-waterfall-easy v-if="list.length" ref="waterfall" :imgsArr="list" :maxCols="10" :gap="12"
            :width="waterfallWidth" :imgWidth="imgWidth" @scrollReachBottom="getList" @click="handlerClick">
            <div class="img-info" slot-scope="props">
                <div class="row1">
                    <span class="name">{{ props.value.comfyWorkflowTaskVO.workflowName }}</span>
                    <span class="like" v-if="model === 'readonly'">
                        <i class="iconfont" :class="props.value.liked ? 'icon-dianzan-liang' : 'icon-dianzan-an'"
                            @click.stop="setLike(props.value)"></i>
                        <span>{{ props.value.totalLikes }}</span>
                    </span>
                </div>
                <div class="row2">
                    <div class="btn btn-text" @click.stop="tongkuang(props)">一键同款</div>
                    <el-popover placement="bottom" width="auto" trigger="hover" content="下载">
                        <div class="btn btn-icon" slot="reference" @click.stop="downImg(props)">
                            <i class="iconfont icon-xiazai"></i>
                        </div>
                    </el-popover>
                    <el-popover v-if="model === 'readonly'" placement="bottom" width="auto" trigger="hover"
                        content="分享工作流">
                        <div class="btn btn-icon" slot="reference" @click.stop="copyTongkuanLink(props)">
                            <i class="iconfont icon-fenxiang"></i>
                        </div>
                    </el-popover>
                    <el-popover v-if="model !== 'readonly'" placement="bottom" width="auto" trigger="hover"
                        content="发布至灵感广场">
                        <div class="btn btn-icon" slot="reference" @click.stop="shareGround(props.value)">
                            <i class="iconfont icon-fenxiang"></i>
                        </div>
                    </el-popover>
                </div>
            </div>
        </vue-waterfall-easy>
    </div>
</template>


<script>

import vueWaterfallEasy from 'vue-waterfall-easy'
import { mapState } from 'vuex'
export default {
    components: {
        vueWaterfallEasy
    },
    props: {
        model: {
            default: ''
        },
        getApiData: {},
        emptyText: {
            default: '暂无数据，换个关键词搜索吧'
        }
    },
    data() {
        return {
            list: [],
            pageNo: 1,
            dataEnd: false,
            dataLoading: false
        }
    },
    watch: {
        isNavCollapsed() {
            this.$nextTick(() => {
                // 直接调用瀑布流组件内部的response方法触发重新计算布局
                if (this.$refs.waterfall) {
                    this.$refs.waterfall.response()
                }
            })
        }
    },
    computed: {
        ...mapState(["isNavCollapsed"]),
        waterfallWidth() {
            return this.isNavCollapsed ? window.innerWidth - 120 : window.innerWidth - 370
        },
        imgWidth() {
            const boxWidth = this.waterfallWidth
            const count = Math.floor(boxWidth / 230)
            return Math.floor(boxWidth / count - 12)
        }
    },
    async created() {
        await this.getList()
    },
    methods: {
        async delHis(it) {
            await this.$confirm('删除后图片不可恢复，确定要删除吗？', '删除任务', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
            await this.$api.post('/ai-main-app/api/comfyWorkflowLog/deleteByIds', [it.listId])
            const index = this.list.findIndex(jt => jt.id === it.id)
            this.list.splice(index, 1)
        },
        handlerClick(e, data) {
            this.imgClick(data.index, 0)
        },
        onScroll(event) {
            if (this.dataEnd) return
            // 滚动到底部距离10px时触发getList
            if (event.target.scrollHeight - event.target.scrollTop - event.target.clientHeight < 10) {
                this.getList()
            }
        },
        imgClick(i, j) {
            this.$previewArg(i, j, this.list, { delHis: this.delHis, model: this.model })
        },
        tongkuang(item) {
            const activeItem = item.value.comfyWorkflowTaskVO
            this.$router.push({
                name: 'workflowBuild',
                query: {
                    id: activeItem.workflowId
                },
                params: {
                    userInputs: activeItem.userInputs
                }
            })
        },
        async shareGround(it) {
            await this.$confirm('是否分享至灵感广场？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消'
            })
            const res = await this.$api.post('/ai-main-app/api/comfyTaskShare/shareTask', {
                taskId: it.comfyWorkflowTaskVO.id,
                imageUrl: it.comfyWorkflowTaskVO.outputImageUrls[0]
            })
            this.$message.success('分享成功')
        },
        downImg(item) {
            this.$myUtils.downImg(item.value.url)
        },
        copyTongkuanLink(item) {
            const activeItem = item.value.comfyWorkflowTaskVO
            const shareLink = `${window.location.origin}/#/workflowBuild?id=${activeItem.workflowId}`;
            this.$myUtils.copyText(shareLink)
            this.$message.success('分享链接复制成功，快来分享吧！')
        },
        setLoading(isLoading) {
            this.dataLoading = isLoading
            const refWaterfall = this.$refs.waterfall
            if (refWaterfall) {
                refWaterfall.isPreloading_c = isLoading
            }
        },
        async setLike(it) {
            await this.$api.post('/ai-main-app/api/comfyTaskShare/like', {
                shareId: it.oId
            })
            it.liked = !it.liked
            it.totalLikes = it.liked ? it.totalLikes + 1 : it.totalLikes - 1
        },
        async getList() {
            if (this.dataLoading || this.dataEnd) {
                this.setLoading(false)
                return false
            }
            const pageSize = 30
            this.setLoading(true)
            const res = await this.getApiData({
                "pageNo": this.pageNo,
                "pageSize": pageSize
            })
            this.setLoading(false)
            this.pageNo++
            const list = []
            res.list.forEach((it) => {
                it.comfyWorkflowTaskVO?.outputImageUrls?.forEach(jt => {
                    // .mp4结尾的视频不显示预览图
                    if (jt.endsWith('.mp4')) return
                    list.push({
                        ...it,
                        ...it.comfyWorkflowTaskVO,
                        url: jt,
                        src: this.$myUtils.generateOssImageUrl(jt),
                        listId: it.id,
                        oId: it.id
                    })
                })
            })
            if (!res.hasNextPage) {
                this.$refs.waterfall.waterfallOver()
                this.dataEnd = true
            }
            this.list = this.list.concat(list)
        },
        handerSearch() {
            this.pageNo = 1
            this.list = []
            this.dataEnd = false
            this.dataLoading = false
            this.getList()
        },
    }
}
</script>


<style lang="less">
.cp-workflow-waterfall {
    width: 100%;
    display: flex;
    justify-content: center;


    .cp-empty {
        padding-top: 50px;
    }



    .vue-waterfall-easy-container {
        height: auto;

        .loading.ball-beat>.dot {
            background-color: #00B55B !important;
        }


        .vue-waterfall-easy-scroll {
            overflow-x: initial;
            overflow-y: initial;

            .vue-waterfall-easy {
                .over{
                    display: none;
                }
            }

            .img-inner-box {
                overflow: hidden;
                border-radius: 8px;
                box-shadow: none;
            }
        }

        .img-box {
            overflow: hidden;
            cursor: pointer;

            &:hover {
                .img-info {
                    opacity: 1;
                    visibility: visible;
                }

                img {
                    transform: scale(1.05);
                }
            }


            .img-inner-box {
                border-radius: 8px !important;

                .img-wraper {
                    img {
                        transition: all 0.3s;
                    }

                }
            }



            .img-info {
                border-radius: 8px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.5s ease;
                cursor: pointer;
                position: absolute;
                top: 6px;
                left: 6px;
                height: calc(100% - 12px);
                width: calc(100% - 12px);
                box-sizing: border-box;
                background: linear-gradient(to bottom,
                        #0D1B3F 0%,
                        rgba(13, 27, 63, 0) 30%,
                        transparent 30%,
                        transparent 70%,
                        rgba(13, 27, 63, 0) 70%,
                        #0D1B3F 100%);
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                font-size: 12px;
                gap: 12px;
                padding: 12px;

                .row1 {
                    display: flex;
                    justify-content: space-between;
                    color: #fff;

                    .like {
                        display: flex;
                        gap: 8px;
                        align-items: center;

                        i {
                            cursor: pointer;
                            font-size: 12px;

                            &.icon-dianzan-liang {
                                color: #F56C6C;
                            }
                        }
                    }
                }

                .row2 {
                    display: flex;
                    color: #fff;
                    gap: 8px;

                    .btn {
                        background: #454C59;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 4px;
                        cursor: pointer;
                        height: 24px;
                        box-sizing: border-box;
                        opacity: 0.8;

                        i {
                            font-size: 12px;
                        }

                        &:hover {
                            opacity: 0.9;
                        }
                    }

                    .btn-text {

                        flex: 1;

                    }

                }
            }
        }

    }


}
</style>