<template>
  <div class="source-material hover-scroll">
    <div class="block-title">灵感广场</div>
    <div class="search-input">
      <el-input placeholder="可搜索作者、品类、颜色、材质、风格" v-model="keywords" @keyup.enter.native="handerSearch">
        <el-button slot="append" type="primary" icon="el-icon-search" @click="handerSearch">搜索</el-button>
      </el-input>
    </div>
    <workflowWaterfall ref="workflow" :getApiData="getApiData" model="readonly"></workflowWaterfall>
  </div>
</template>
<script>
import workflowWaterfall from './workflow-waterfall'


export default {
  components: {
    workflowWaterfall
  },
  data() {
    return {
      keywords: '',
    };
  },

  computed: {
  },
  beforeDestroy() {
  },
  methods: {
    handerSearch() { 
      this.$refs.workflow.handerSearch()
    },
    async getApiData({ pageNo, pageSize }) {
      return await this.$api.post('/ai-main-app/api/comfyTaskShare/listPage', {
        data: {
          keywords: this.keywords
        },
        "orderBy": "id",
        pageNo,
        pageSize
      })
    }
  },
  mounted() {
  }
};
</script>

<style lang="less">
#app .source-material {
  height: 100%;
  box-sizing: border-box;

  .block-title {
    font-size: 22px;
    font-weight: bold;
    line-height: normal;
    margin-bottom: 16px;
    text-align: center;
  }

  .search-input {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 32px;

    .el-input {
      width: 400px;

      input.el-input__inner {
        border-radius: 1000px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-color: #00B55B;
      }

      .el-input-group__append {
        border-color: #00B55B;
        border-radius: 0;
        border-top-right-radius: 1000px;
        border-bottom-right-radius: 1000px;
        overflow: hidden;
        background-color: #00B55B;

        .el-button {
          border: 0;
          height: auto;
        }

      }

      .el-button {
        background-color: #00B55B;
        color: #fff;
      }

    }

    .btn1 {
      margin: 0;
      width: 100px;
      height: 32px;
    }
  }


  .cp-workflow-waterfall{
    height: calc(100% - 160px);
  }




}
</style>