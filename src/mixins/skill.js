import config from '@/config'
import { mapState, mapMutations } from "vuex";

export default {
    computed: {
        ...mapState(["userInfo"])
    },
    methods: {
        shareSkill(skill) {
            const shareLink = `${window.location.origin}/#/share-skill?id=${skill.id}`;
            this.$myUtils.copyText(shareLink)
            this.$message.success('分享链接复制成功，快来分享吧！')
        },
        upEventSkill(skill) {
            if (!this.userInfo.name) {
                return
            }
            const basicInfo = {
                name: skill.showName,
                menu_id: skill.id,
                event_type: 'SKILL',
                event_name: '技能',
                report_app_name: 'ZEUS',
                loginName: this.userInfo.loginName,
                userName: this.userInfo.name,
                report_app_tenant: config.TENANT_ID,
                report_app_ext_tenant: config.EXT_TENANT_ID
            }
            this.$upEvent('Click', basicInfo)
        },
        async jump(skill, inInput) {
            this.upEventSkill(skill)
            // 链接跳转
            if (inInput && skill.interactionType === "PROMPT_IN") {
                this.addTemp(skill)
                return
            }
            if (skill.interactionType === "LINK_FORWARD") {
                window.open(skill.value);
            }
            // 提示词填入
            if (skill.interactionType === "PROMPT_IN") {
                this.$router.push({ path: `/chat`, query: { skill: skill.id } });
            }
            // 进入场景
            if (skill.interactionType === "SCENE_IN") {
                const res = await this.$api.get('/ai-main-app//api/chat/startConversationByScene', {
                    params: {
                        conversationId: this.$route.query.conversationId,
                        appCode: skill.appCode,
                        sceneCode: skill.sceneCode
                    }
                })
                if (this.$route.query.conversationId !== res.conversationId) { // 其他页面
                    this.$router.push({ path: 'chat', query: { conversationId: res.conversationId } })
                } else if (this.flashMsgList) {   // 首页
                    this.flashMsgList()
                } else {  // 弹窗
                    this.$emit('flashMsgList')
                }
            }
            // 免登登录
            if (skill.interactionType === "NO_LOGIN_FORWARD") {
                if (await this.checkExt()) {
                    this.getExt(skill.value);
                }
            }
            // 应用跳转
            if (skill.interactionType === "APPLICATION_IN") {
                const res = await this.$api.get('/ai-main-app/api/chat/startConversationByScene', {
                    params: {
                        appCode: skill.appCode,
                        sceneCode: skill.sceneCode
                    }
                })
                this.$router.push({ path: skill.value, query: { conversationId: res.conversationId } })
            }
            // 刷新消息列表
            this.$store.dispatch("initList", this.searchValue);
        },
        async checkExt() {
            const res = await this.$api.get('/user/api/user/self?target=test', { headers: { 'retHeader': 'true' }, })
            const headers = res.headers
            const hasExt = headers['content-type'] === 'application/json;charset=UTF-8;X-server=crawler'
            if (!hasExt) {
                this.$confirm('检测到您还没安装浏览器插件，要先安装插件才能使用快捷登录功能，如何安装请看：<a target="_blank" style="color:#00B55B" href="https://syounggroup.feishu.cn/wiki/F1aWwN637ihs8jk0LPacWbuqnJc">账号共享插件安装手册</a>', '提示', {
                    confirmButtonText: '确定',
                    showCancelButton: false,
                    dangerouslyUseHTMLString: true,
                    type: 'warning'
                })
            }
            return hasExt;
        },
        async getExt(platformCode) {
            let ret = await this.$api.get('/hestia-service/api/crawlerAccount/searchAccountByPlatform', { params: { platformCode } })
            if (ret.platformCode === undefined) {
                this.$message.error('找不到可以使用的账号，请联系管理员绑定账号!')
                return
            }
            if (ret.cookies === undefined) {
                this.retryCount++
                if (this.retryCount <= 5) {
                    if (this.retryCount === 1) {
                        this.$message.error('当前登录人数较多，正在为您分配账号，预计需要2分钟，请耐心等待。')
                    }
                    setTimeout(() => this.getExt(platformCode), 40000)
                } else {
                    this.$message.error('当前登录人数过多，请稍后再试!')
                }
                return
            }

            this.$api.post('/user/api/user/self', {
                data: ret.cookies,
                userAgent: ret.userAgent,
                target: ret.platformCode,
                loginName: ret.loginName,
                accountId: ret.accountId,
                accountName: ret.accountName,
                extensionVersion: ret.extensionVersion
            }, {
                params: {
                    target: encodeURIComponent(ret.platformCode),
                    loginName: encodeURIComponent(ret.loginName),
                    accountId: encodeURIComponent(ret.accountId),
                    accountName: encodeURIComponent(ret.accountName),
                    extensionVersion: encodeURIComponent(ret.extensionVersion)
                }
            })
        },
    }
}
