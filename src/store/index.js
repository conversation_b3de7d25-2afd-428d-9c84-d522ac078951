import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

const initSceneFeatureSwitches = {
	"appCode": "ZEUS_CHAT",
	"appName": "ZEUS对话应用",
	"sceneCode": "GENERAL_CHAT",
	"sceneName": "",
	"allowClearContext": true, // 允许清空上下文
	"allowDelete": true, // 允许删除
	"allowEmptyAttachmentSend": false, // 是否允许空附件发送开关
	"allowSharing": true, // 是否允许分享开关
	"enableDeepThinkingSwitch": true, // 深度思考开关
	"enableFeishuDocSupport": true, // 飞书文档支持开关
	"enableFileUpload": true, // 文件上传开关
	"enableGuessQuestion": true, // 猜你想问开关
	"enableImageUpload": true, // 图片上传开关
	"enableInput": true,   // 允许输入框
	"enableLargeModelSwitch": true, // 大模型切换开关
	"enableWebLinkSupport": true, // 网页链接支持开关
	"enableWebSearchSwitch": true, // 联网开关
	"allowReGenerate": true, // 是否允许重新生成开关
	"enableSkillSwitch": true, // 是否允许技能选择
	"sceneInputPlaceHolder": '发消息、输入@选择技能、粘贴网页链接/飞书文档/图片/文件作为附件'
}

const store = new Vuex.Store({
	state: {
		userInfo: {},
		navInfo: {
			name: '',
			type: '', // 是否置顶
			id: '' // 有id代表是对话菜单
		},
		isNavCollapsed: false,
		pinList: [],
		latest: [],
		isCompleted: true,
		sceneFeatureSwitches: {
			...initSceneFeatureSwitches
		}
	},
	mutations: {
		setUserInfo(state, o) {
			state.userInfo = {
				...state.userInfo,
				...o
			}
		},
		setIsCompleted(state, value) {
			state.isCompleted = value
		},
		setSceneFeatureSwitches(state, switches) {
			state.sceneFeatureSwitches = {
				...state.sceneFeatureSwitches,
				...switches
			}
		},
		initSceneFeatureSwitches(state) {
			state.sceneFeatureSwitches = {
				...state.sceneFeatureSwitches,
				...initSceneFeatureSwitches
			}
		},
		setNavColspan(state, value) {
			state.isNavCollapsed = value
		},
		setPinList(state, pinList) {
			state.pinList = pinList;
		},
		setLatest(state, latest) {
			state.latest = latest;
		}
	},
	actions: {
		async initList({ commit }, nameLike = '') {
			try {
				const res = await Vue.prototype.$api.post(
					"/ai-main-app/api/conversation/listMyLatest",
					{ nameLike }
				);
				commit('setPinList', res.pinList);
				commit('setLatest', res.latest);
			} catch (error) {
				console.error('Failed to fetch conversation list:', error);
			}
		}
	}
});

export default store;
