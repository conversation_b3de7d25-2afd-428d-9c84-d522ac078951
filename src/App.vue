<template>
  <div id="app">
    <template>
      <router-view class="page-full" v-if="fullScreen" />
      <div v-else class="page">
        <div class="main">
          <div :class="['left', { collapsed: isNavCollapsed }]">
            <cp-nav ref="nav" />
          </div>
          <div :class="['right', { expanded: isNavCollapsed }]">
            <!-- 顶部菜单栏，需要展示菜单的名称 -->
            <div class="top-menu" :class="{ noBorder: !navInfo.type }"
              v-if="navInfo.type && !isNavCollapsed || isNavCollapsed">
              <img v-if="isNavCollapsed" class="shouqi-logo" src="@/assets/logo/single-logo.png" alt="" srcset="" />
              <i v-if="isNavCollapsed" class="iconfont icon-caidanzhankai" @click="toggleNavCollapse"></i>
              <div v-if="isNavCollapsed && navInfo.type" class="fengeshuxian"></div>
              <!-- 有id是对话菜单，可以操作置顶 -->
              <div class="nav-item" v-if="navInfo.type">
                <i class="iconfont icon-Pin" v-if="navInfo.type === 'pin'"></i>
                <span class="title" v-if="navInfo.id !== activeId" :title="navInfo.name">
                  <span class="chat-type is-app" v-if="isApp(navInfo)">
                    <i class="iconfont" :class="[isApp(navInfo) ? 'icon-a-yingyongyingyongguanli' : 'icon-huihua1']"></i>
                    <span v-if="isApp(navInfo)">智能体</span>
                  </span>
                  <span>{{ navInfo.name }}</span>
                </span>
                <el-input v-else class="rename-input" v-model="newName" @blur="handleSChange"
                  placeholder="请输入内容"></el-input>
                <Edit :item="navInfo" :isPinned="navInfo.type === 'pin'" @update="handleUpdate" @rename="startRename" />
              </div>
              <!-- 路由菜单，无操作置顶 -->
              <!-- <span v-else class="title">{{ navInfo.name }}</span> -->
            </div>
            <div class="right-content"
              :class="{ rightcContentChat: navInfo.type || isNavCollapsed, rightcContentChat2: !navInfo.type && !isNavCollapsed }">
              <router-view />
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import config from './config'
import { mapState, mapMutations } from "vuex";
import Edit from "@/components/cpNav/comps/Edit.vue";

export default {
  name: "App",
  components: {
    Edit
  },
  computed: {
    ...mapState(["isNavCollapsed", "userInfo"]), // 使用 Vuex 的状态
    fullScreen() {
      return this.$route.meta.fullScreen;
    },
    menu() {
      if (!this.userInfo.name) {
        return
      }

      return this.userInfo.menus.find(it => it.href === this.$route.path)
    },
    navInfo() {
      if (!this.$route.query.conversationId) {
        return {}
      }
      let item = this.$store.state.pinList.find(it => it.id === this.$route.query.conversationId)
      if (item) {
        return {
          ...item,
          type: 'pin'
        }
      }
      item = this.$store.state.latest.find(it => it.id === this.$route.query.conversationId)
      if (item) {
        return {
          ...item,
          type: 'unpin'
        }
      }
      return {}
    }
  },
  watch: {
    fullScreen() {
      this.getUserInfo();
    },
    navInfo: {
      immediate: true,
      handler(v) {
        document.title = v.name || 'ZEUS-懂水羊更懂你的AI助手'
      }
    },
    $route: {
      immediate: true,
      handler() {
        if (this.$myUtils.isMobile()) {
          this.setNavColspan(true);
        }
        this.upEventPageView()
      }
    }
  },
  data() {
    return {
      activeId: "", // 被重命名选中的会话
      newName: "", // 重命名
    };
  },
  methods: {
    ...mapMutations(["setNavColspan"]), // 使用 Vuex 的 mutation
    isApp(item) {
      return item.appCode !== 'ZEUS_CHAT'
    },
    toggleNavCollapse() {
      this.setNavColspan(!this.isNavCollapsed); // 更新 Vuex 状态
    },
    upEventPageView() {
      if (!this.userInfo.name || !this.menu) {
        return
      }
      const basicInfo = {
        name: this.menu.name,
        menu_id: this.menu.id,
        report_app_name: 'ZEUS',
        loginName: this.userInfo.loginName,
        userName: this.userInfo.name,
        report_app_tenant: config.TENANT_ID,
        report_app_ext_tenant: config.EXT_TENANT_ID
      }
      this.$upEvent('PageView', { ...basicInfo })
    },
    async handleSChange() {
      if (this.newName && this.activeId) {
        try {
          if (!this.newName) {
            this.$message.error('不能为空');
            return;
          }
          if (this.newName.length > 50) {
            this.$message.error(`对话标题不能超过50个字符，现有长度${this.newName.length}，请删减`);
            return;
          }
          await this.$api.post("/ai-main-app/api/conversation/rename", {
            id: this.activeId,
            name: this.newName,
          });
          this.activeId = "";
          this.newName = "";
          this.$refs.nav.initList(); // 调用 cpNav.vue 中的 initList 方法
        } catch (error) {
          console.error("Error renaming:", error);
        }
      }
    },
    startRename(id) {
      if (this.navInfo.id === id) {
        this.activeId = id;
        this.newName = this.navInfo.name;
      }
    },
    handleUpdate() {
      this.$refs.nav.initList(); // 调用 cpNav.vue 中的 initList 方法
    },
    openHelp() {
      window.open(
        "https://syounggroup.feishu.cn/wiki/GLk5wdeHyi8dNYkwyKucNDqknTc"
      );
    },
    // 获取用户信息
    async getUserInfo() {
      if (this.$route.path !== "/login") {
        const res = await Promise.all([
          this.$api.get("/user/api/user/self"),
          this.$api.get("/ai-main-app/api/userInfo/getAvatarUrl"),
        ]);
        const userInfo = {
          ...res[0].user,
          menus: res[0].menus,
          ...res[1],
        };
        this.$store.commit("setUserInfo", userInfo);
        this.upEventPageView()
      }
    },
    openWorkflow() {
      window.open("http://************:8188/");
    },
  },
  created() {
    this.getUserInfo();
    // this.setInitialNavInfo();
  },
};
</script>

<style lang="less">
@import url(./glob.less);

.page {
  height: 100vh;
  width: 100vw;
  background: #f0f2f5;

  .header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.14);
    background: rgba(6, 13, 18, 0.86);
    backdrop-filter: blur(7.900000095367432px);
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-sizing: border-box;

    .logo {
      cursor: pointer;
      height: 24px;
    }

    &>.right {
      display: flex;
      align-items: center;

      .col1 {
        cursor: pointer;
        height: 32px;
        color: #fff;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        border-radius: 100px;
        background: rgba(255, 255, 255, 0.09);
        display: inline-flex;
        padding: 0 13px;
        align-items: center;
        margin-right: 13px;
        line-height: 1;
        height: 32px;

        &:hover {
          background: rgba(255, 255, 255, 0.18);
        }

        i {
          margin-right: 10px;
          font-size: 16px;
        }
      }

      .cp-user-header {
        margin-left: 16px;
      }
    }
  }

  .main {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    display: flex;

    &>.left {
      width: 250px; // 默认宽度
      transition: width 0.3s; // 添加过渡效果

      &.collapsed {
        width: 0; // 收起时的宽度
      }

      border-right: 1px solid rgba(255, 255, 255, 0.11);
      background: #F3F5F8;
    }

    &>.right {
      flex: 1;
      background: #fff;
      overflow: auto;
      height: 100%;
      // border-top-left-radius: 30px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.03);
      border-radius: 16px 0 0 0;

      .top-menu {
        height: 50px;
        display: flex;
        align-items: center;
        padding: 0 24px;
        border-bottom: 0.5px solid #E6E8EB;
        background: #ffffff;
        z-index: 1000;

        .shouqi-logo {
          width: 30px;
          margin-right: 5px;
        }

        .fengeshuxian {
          width: 0.5px;
          height: 20px;
          background: #E6E8EB;
          margin: 0 8px;
        }

        .chat-type {
          font-size: 12px;
          line-height: 14px;
          font-weight: normal;
          display: flex;
          gap: 4px;
          align-items: center;
          padding: 2px 4px;

          &.is-app {
            border-radius: 2px;
            background-color: #E0F2E9;
            color: #00B55B;

            i {
              color: #00B55B;
            }
          }

          i {
            font-size: 14px;
            color: #B6BAC5;
          }
        }

        .nav-item {
          display: flex;
          align-items: center;
          line-height: 22px;
          // width: 80%;
        }

        .title {
          margin-right: 8px;
          color: #003219;
          // max-width: 80%;
          max-width: 400px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: flex;
          gap: 4px;
          // flex-grow: 1;
        }

        .iconfont {
          cursor: pointer;
          font-size: 22px;
          margin-right: 4px;
          color: #6e768c;
        }

        .icon-relieve-full {
          font-size: 16px;
        }

        .page-title {
          font-size: 16px;
          font-weight: bold;
        }
      }

      .noBorder {
        border: none;
      }

      .noMenu {
        display: none;
      }

      .right-content {
        height: 100vh;
        overflow-y: auto;
      }

      .rightcContentChat {
        height: calc(100vh - 51px);
      }

      .rightcContentChat2 {
        &>div {
          padding-top: 51px;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
