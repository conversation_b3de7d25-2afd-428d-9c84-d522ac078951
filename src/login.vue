<template>
  <div class="page-login" v-if="!isFeishu || !isProduction">
    <div class="left">
      <img src="./assets/loginBk.png" alt="" srcset="">
    </div>
    <div class="right">
      <div class="login-wrap">
        <img class="logo" src="@/assets/logo/竖版LOGO.png" alt="">
        <!-- <div class="logo-text1">ZEUS</div> -->
        <div class="logo-text2">懂水羊更懂你的AI助手</div>
        <el-input class="username" placeholder="花名拼音" v-model="form.username" size="medium" @keyup.enter.native="login">
          <template slot="prepend">
            <i class="iconfont icon-zhanghao"></i>
          </template>
        </el-input>
        <el-input class="password" placeholder="密码" v-model="form.password" type="password" size="medium"
          @keyup.enter.native="login">
          <template slot="prepend">
            <i class="iconfont icon-mima"></i>
          </template>
        </el-input>
        <div class="btn1" @click="login">登录</div>
        <div class="desc">水羊数字中心出品</div>
      </div>

    </div>
  </div>
</template>

<script>
import { requestAuthCode } from '@/utils/feishu.js'
import config from './config';
import { isChrome } from '@/utils/browser'
import BrowserWarning from '@/components/BrowserWarning.vue'
export default {
  name: "Login",
  data() {
    return {
      isFeishu: !!window.tt,
      form: {
        username: '',
        password: ''
      }
    };
  },
  computed: {
    isProduction() {
      return process.env.VUE_APP_MODE === 'production'
    }
  },
  async created() {
    if (!isChrome() && !this.$myUtils.isMobile()) {
    const h = this.$createElement;
      this.$msgbox({
        title:'',
        message: h(BrowserWarning),
        showCancelButton: false,
        showConfirmButton: false,
        closeOnClickModal: true,
        closeOnPressEscape: true,
        showClose: true,
        customClass: 'browser-warning-dialog'
      })
    }
    // if (await this.initOALogin()) {
    //   return
    // }
    if ((isChrome() ||  this.$myUtils.isMobile())   && await this.initFeishuLogin()) {
      return
    }
  },
  methods: {
    goPage() { 
      const backUrl = this.$route.query.backUrl
      if (backUrl) {
        this.$router.push(decodeURIComponent(backUrl))
      } else {
        this.$router.push('/')
      }
    },
    async initFeishuLogin() {
      const authData = await requestAuthCode(false)
      if (!authData) {
        return
      }
      const { sessionId } = await this.$api.get('/dingtalk/api/feishuUser/userTokenLoginByCode', {
        params: { appId: config.appId, code: authData.code, type: 'WEB' }
      })
      if (sessionId) {
        this.$myUtils.setCookie(sessionId)
        this.goPage()
        return true
      }
      return false
    },
    async initOALogin() {
      const res = await this.$api.get('/user/ssoReturn', {
        withCredentials: true,
      })
      if (res.sessionId) {
        this.$myUtils.setCookie(res.sessionId)
        this.goPage()
        return true
      }
      return false
    },
    async login() {
      if (!this.form.username) {
        this.$message.error('请输入用户名')
        return false
      }
      if (!this.form.password) {
        this.$message.error('请输入密码')
        return false
      }
      const handleData = await this.handlePsd(this.form.username, this.form.password)
      const { identifyKey,username,password } = handleData
      const data = await this.$api.post('/ai-main-app/login', {
        identifyKey,
        loginName: username,
        password: password
      })
      this.$myUtils.setCookie(data.sessionId)
      this.goPage()
    },
    // 获取后台秘钥,加密数据
    async handlePsd(username, psd) {
      const data = await this.$api.get('/ai-main-app/fetchIdentify')
          const pKey = data
          var encrypt = new window.JSEncrypt()
          encrypt.setPublicKey(pKey)
          return {
            identifyKey: pKey,
            password: encrypt.encrypt(psd),
            username: encrypt.encrypt(username),
          }
    },
  }
};
</script>

<style lang="less">
@media screen and (max-height: 600px) {
  div.page-login {
    .right {
      .login-wrap {
        margin-top: 0px;

        .desc {
          bottom: 14px;
          font-size: 14px;
        }
      }
    }
  }
}


.page-login {
  display: flex;
  height: 100vh;
  width: 100vw;

  .left {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
   background-color: #DEDCDF;

    img {
      max-width: 100%;
    }
  }

  .right {
    background-color: #FFFFFF;
    width: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .login-wrap {
      width: 348px;
      text-align: center;
      margin-top: -90px;

      .logo {
        width: 100px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }

      .logo-text1 {
        font-size: 32px;
        font-weight: 600;
        margin-bottom: 20px;
        background: linear-gradient(180deg, #7CD5A9 0%, #00B55B 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        letter-spacing: 0.3em;
      }

      .logo-text2 {
        font-size: 16px;
        font-weight: 600;
        
        margin-bottom: 60px;
        letter-spacing: 0.1em;
        margin-top: 8px;
      }

      .el-input {
        background: #D3F3EF;
        height: 48px;
        border-radius: 8px;

        .el-input__inner {
          background: #fff;
          height: 100%;
          padding-left: 24px;
          border-color: #E6E8EB;
          border-top-right-radius: 8px;
          border-bottom-right-radius: 8px;
        }

        .el-input-group__prepend {
          border-top-left-radius: 8px;
          border-bottom-left-radius: 8px;
          border-color: #E6E8EB;
          background-color: #F3F5F8;
          color: #9EA4B2;
        }
      }

      .username {
        margin-bottom: 16px;
      }

      .password {
        margin-bottom: 40px;
      }

      .btn1 {
        letter-spacing: 0.28em;
      }
    }
  }

  .desc {
    color: #6E768C;
    font-size: 16px;
    position: absolute;
    bottom: 28px;
    text-align: center;
    width: 348px;
  }
}
</style>