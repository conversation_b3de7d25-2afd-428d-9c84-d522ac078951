import Vue from 'vue'
import Router from 'vue-router'
import Login from './login.vue'
import config from './config'

let routes = [
    {
        path: '/login',
        name: 'login',
        component: Login,
        meta: {
            fullScreen: true
        }
    },
    {
        path: '/',
        redirect: '/chat'
    },
    {
        path: '/chat',
        name: 'chat',
        component: () => import('./pages/chat/index.vue')
    },
    {
        path: '/share-skill',
        name: 'shareSkill',
        component: () => import('./pages/shareSkill/index.vue')
    },
    {
        path: '/workflowApplication',
        name: 'workflowApplication',
        component: () => import('./pages/workflowApplication/index.vue')
    },
    {
        path: '/workflowBuild',
        name: 'workflowBuild',
        component: () => import('./pages/workflowBuild/index.vue'),
        meta: {
            fullScreen: true
        }
    },
    {
        path: '/sourceMaterial',
        name: 'sourceMaterial',
        component: () => import('./pages/sourceMaterial/index.vue')
    },
    {
        path: '/toolbox',
        name: 'toolbox',
        component: () => import('./pages/toolbox/index.vue')
    },
    {
        path: '/help',
        name: 'help',
        component: () => import('./pages/help/index.vue')
    },
    {
        path: '/userCenter',
        name: 'userCenter',
        component: () => import('./pages/userCenter/index.vue')
    },
    {
        path: '/liveCheck',
        name: 'liveCheck',
        component: () => import('./pages/liveCheck/index.vue')
    },
    {
        path: '/aiSkillsLibrary',
        name: 'aiSkillsLibrary',
        component: () => import('./pages/aiSkillsLibrary/index.vue')
    },
    {
        path: '/agent/xhs-market-campaing',
        name: 'agentXhsMarketCampaing',
        component: () => import('./pages/agentXhsMarketCampaing/index.vue')
    },
    {
        path: '/agent/live-talent-recommend',
        name: 'agentLiveTalentRecommend',
        component: () => import('./pages/agentLiveTalentRecommend/index.vue')
    },
]


const router = new Router({
    routes
})

router.beforeEach(async (to, from, next) => {
    next()
})

Vue.use(Router)



window.$router = router

export default router