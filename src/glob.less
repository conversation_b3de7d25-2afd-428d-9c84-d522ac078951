body {
    font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, SF Pro SC, SF Pro Display, SF Pro Icons, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif !important;
}


html,
* {


    scrollbar-width: inherit;



    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        /* 滚动条滑块的颜色 */
        border-radius: 8px;
        /* 滚动条滑块的圆角 */
    }

    ::-webkit-scrollbar-track {
        background-color: transparent;
        border-radius: 8px;
    }

    .hover-scroll,
    .el-textarea__inner,
    .vue-waterfall-easy-scroll {
        &:hover {
            &::-webkit-scrollbar-thumb {
                background-color: rgba(0, 0, 0, 0.2);
            }
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0);
        }
    }

}




html,
body {
    margin: 0;
    padding: 0;
    background: #F3F5F8;
    color: rg<PERSON>(0, 0, 0, 0.85);

    .page-title {
        display: flex;
        align-items: center;
        height: 54px;
        border-bottom: 1px solid #E0E0E0;
        padding-left: 24px;
        font-size: 18px;
        font-weight: 600;

    }

    @media screen and (max-width: 768px) {
        .el-popup-parent--hidden {
            padding-right: 0 !important;
        }
    }


    .el-button--primary.is-plain:focus,
    .el-button--primary.is-plain:hover {
        background: #32C079;
        border-color: #32C079;
        color: #fff;
    }



    .el-drawer__header {
        margin: 0;
        margin-bottom: 0;

        font-size: 16px;
        font-weight: bold;
        padding: 0;
        padding: 12px 20px;
    }

    // 重写全局dialog
    .el-dialog__wrapper {
        display: flex;
        align-items: center;
        justify-content: center;


        .el-dialog {
            background-color: #ffffff;
            border: 1px solid #E6E8EB;
            border-radius: 16px;
            margin: 0;

            .el-dialog__header {
                padding: 12px 18px;
                border-bottom: 1px solid #E6E8EB;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .el-dialog__title {
                    font-size: 14px;
                    font-weight: 600;

                    line-height: 24px;
                }

                .el-dialog__headerbtn {
                    position: unset;

                    .el-dialog__close {

                        font-size: 16px;
                        line-height: 24px;
                    }
                }

            }

            .el-dialog__body {
                padding: 0;
            }

            .el-dialog__footer {
                padding: 14px 18px;
                border-top: 1px solid rgba(255, 255, 255, 0.27);

                .dialog-footer {
                    display: flex;
                    justify-content: flex-end;
                    gap: 12px;
                }

                .dialog-footer-between {
                    display: flex;
                    justify-content: space-between;

                    .btn-left {
                        display: flex;
                        gap: 12px;
                    }

                    .btn-right {
                        display: flex;
                        gap: 12px;
                    }
                }
            }

        }
    }

    // 重写滑块样式
    .el-slider {

        .el-slider__bar {
            background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
            height: 4px;
        }

        .el-slider__button {
            border: none;
            background-color: #32C079;
        }

        .el-slider__input {
            width: 70px;

            .el-input-number__decrease,
            .el-input-number__increase {
                display: none;
            }

            .el-input {
                border: 1px solid rgba(0, 0, 0, 0.09);
                border-radius: 100vw;
            }

            .el-input__inner {
                padding: 0px;
                border-radius: 100vw;
                border: none;
            }
        }

        .el-slider__runway {
            background-color: #F3F5F8;
            height: 4px;
        }

        .el-slider__runway.show-input {
            margin-right: 90px;
        }

        .el-slider__button-wrapper {
            top: -16px;
        }
    }




    .el-message-box__wrapper {
        .el-message-box {
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            background: #fff;
            width: 400px;
            min-height: 180px;
            box-sizing: border-box;
            padding: 24px;

            .el-message-box__header {
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .el-message-box__title {
                    color: #0E181F;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

                .el-message-box__headerbtn {
                    position: unset;

                    .el-message-box__close {
                        font-size: 20px;
                        color: #0E181F;
                        transition: all 0.2s;

                        &:hover {
                            transform: scale(1.2);
                        }
                    }
                }
            }

            .el-message-box__content {
                color: #0E181F;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                padding: 0;
                padding-top: 12px;
                padding-bottom: 32px;
            }

            .el-message-box__btns {
                padding: 0;

                .el-button:not(.el-button--primary) {
                    margin: 0;
                    display: inline-flex;
                    box-sizing: border-box;
                    width: 100px;
                    height: 36px;
                    padding: 8px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 100px;
                    border: 1px solid rgba(0, 0, 0, 0.1);
                    background: rgba(0, 0, 0, 0.02);
                    box-sizing: border-box;
                    color: #08493E;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    margin-right: 16px;

                    &:hover {
                        background: rgba(0, 0, 0, 0.03);
                    }
                }

                .el-button.el-button--primary {
                    margin: 0;
                    display: inline-flex;
                    box-sizing: border-box;
                    width: 100px;
                    height: 36px;
                    padding: 8px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 100px;
                    background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
                    border: none;
                    box-sizing: border-box;
                    color: #08493E;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;

                    &:hover {
                        opacity: 0.8;
                    }
                }
            }
        }
    }


    .el-popover {
        border-radius: 4px;
        border: none;
        padding: 4px 8px;
        font-size: 12px;
        min-width: 30px;
        max-width: 290px;
        color: #6E768C;

        &.inline {
            min-width: 40px;
            text-align: center;
            width: auto !important;

        }




        // &[x-placement^=right] .popper__arrow {
        //     border-right-color: #494E52;

        //     &::after {
        //         border-right-color: #494E52;
        //     }
        // }

        // &[x-placement^=top] .popper__arrow {
        //     border-top-color: #494E52;

        //     &::after {
        //         border-top-color: #494E52;
        //     }
        // }

        // &[x-placement^=bottom] .popper__arrow {
        //     border-bottom-color: #494E52;

        //     &::after {
        //         border-bottom-color: #494E52;
        //     }
        // }

        .popper__arrow {}
    }

    .el-message--error {
        border-radius: 610px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        background: #232F38;
        display: inline-flex;
        padding: 14px 24px;
        align-items: center;
        min-width: 50px;

        .el-message__icon {
            color: #F93920;
            font-family: "iconfont" !important;
            font-size: 16px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;

            &:before {
                content: '\e6b5'
            }
        }

        .el-message__content {
            color: rgba(255, 255, 255, 0.69);
        }
    }

    .el-message--success {
        border-radius: 610px;
        border: 1px solid rgba(255, 255, 255, 0.15);
        background: #232F38;
        display: inline-flex;
        padding: 14px 24px;
        align-items: center;
        min-width: 50px;

        .el-message__icon {
            color: #3BB346;
            font-family: "iconfont" !important;
            font-size: 16px;
            font-style: normal;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;

            &:before {
                content: '\e6b3'
            }
        }

        .el-message__content {
            color: rgba(255, 255, 255, 0.69);
        }
    }

    .el-select-dropdown {
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.16);
        background: #fff;
        display: flex;
        padding: 12px;
        flex-direction: column;

        .popper__arrow {
            display: none;
        }

        .el-select-dropdown__list {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .el-select-dropdown__item {
            font-size: 14px;
            display: flex;
            padding: 12px 20px;
            align-items: center;
            border-radius: 610px;
            font-weight: normal;
            border: none;
            width: 100%;

            &:hover,
            &.hover {
                background-color: rgba(255, 255, 255, 0.16);
            }


            &.selected {
                color: #00B55B;
            }
        }
    }

    .el-dropdown-menu {
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.16);
        // background: #0F171D;
        background: #ffffff;
        display: flex;
        padding: 6px;
        flex-direction: column;

        .popper__arrow {
            display: none;
        }

        .el-dropdown-menu__item {
            // color: #fff;
            color: #0d1c40;
            font-size: 14px;
            display: flex;
            padding: 2px 10px;
            align-items: center;
            border-radius: 610px;
            line-height: 24px;
            border: none;

            &.is-active,
            &.is-active:hover {
                color: #00B55B;
                background-color: #E0F2E9;
            }

            &:hover {
                color: #00B55B;
                background-color: transparent;
            }


            .iconfont {
                font-size: 16px;
                margin-right: 10px;
            }
        }
    }


    .el-tabs:not(.el-tabs--border-card) {
        .el-tabs__item {
            font-size: 14px;
            line-height: normal;
            color: #6E768C;
            // background: linear-gradient(90deg, #8EF379 0%, #22C3AD 100%);
            // background-clip: text;
            // -webkit-background-clip: text;
            // -webkit-text-fill-color: transparent;
            height: 26px;
        }

        .el-tabs__item:hover {
            color: #00B55B;
        }

        .is-active {
            color: #00B55B;
        }

        .el-tabs__active-bar {
            height: 3px;
            background-color: #00B55B;
        }

        .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: rgba(255, 255, 255, 0.34);
        }
    }

    .el-image {
        .el-image__placeholder {
            background-color: transparent;
        }

        .el-image__error {
            background: #04121B;
            color: #828588;
        }
    }



}

#app {
    // color: #08493E;
    // font-family: "PingFang SC";

    // input::placeholder,
    // textarea::placeholder {
    //     color: rgba(255, 255, 255, 0.46);
    // }

    // input,
    // textarea {
    //     color: #fff;
    //     font-size: 14px;

    // }



}


.el-image.zoom {
    img {
        transition: all 0.3s;
    }

    &:hover {
        img {
            transform: scale(1.1);
        }
    }
}

// 特定样式

.btn1 {
    box-sizing: border-box;
    display: flex;
    height: 48px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    border-radius: 160px;
    background: #00B259;
    color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    box-sizing: border-box;
    cursor: pointer;

    &.small {
        height: 32px;
        font-weight: normal;
        display: inline-flex;
        padding: 0 10px;
        min-width: 72px;
        line-height: 32px;
        font-size: 14px;
    }

    &:hover {
        background-color: #32C079;
    }

    i {
        font-size: 24px;
        margin-right: 10px;
    }

    &.disabled {
        color: #7CD5A9;
        pointer-events: none;
    }
}

.btn2 {
    box-sizing: border-box;
    display: flex;
    height: 48px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    border-radius: 160px;
    border: 1px solid #00B259;
    background-color: #fff;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    box-sizing: border-box;
    cursor: pointer;
    color: #00B259;

    &.small {
        height: 32px;
        font-weight: normal;
        display: inline-flex;
        padding: 0 10px;
        min-width: 72px;
        line-height: 32px;
        font-size: 14px;
    }

    &:hover {
        opacity: 0.8;
    }

    i {
        font-size: 24px;
        margin-right: 10px;
    }

    &.disabled {
        color: rgba(0, 0, 0, 0.46);
        background: #2A353B;
        pointer-events: none;
    }
}


.btn3 {
    box-sizing: border-box;
    display: flex;
    height: 30px;
    padding: 6px 8px;
    justify-content: center;
    align-items: center;
    border-radius: 160px;
    border: 1px solid #E6E8EB;
    font-size: 12px;
    font-style: normal;
    font-weight: normal;
    line-height: normal;
    box-sizing: border-box;
    cursor: pointer;


    &:hover {
        color: #00B259;
        border-color: #00B259;
    }

    i {
        font-size: 16px;
        margin-right: 4px;
    }
}

.btn-opt {
    box-sizing: border-box;
    display: flex;
    height: 48px;
    padding: 10px;
    justify-content: center;
    align-items: center;
    border-radius: 160px;
    border: 1px solid #00B259;
    // background-color: rgba(0, 0, 0, 0.1);
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    box-sizing: border-box;
    cursor: pointer;
    height: 32px;
    font-weight: normal;
    display: inline-flex;
    padding: 0 10px;
    min-width: 72px;
    line-height: 32px;
    gap: 4px;
    color: #00B55B;
}

// 重写loading样式
.el-loading-spinner {
    background-image: url('./assets/loading-small.png');
    /* 替换为你的图标路径 */
    background-repeat: no-repeat;
    // background-size: contain; /* 根据需要调整 */
    height: 32px;
    /* 根据需要调整 */
    width: 32px !important;
    /* 根据需要调整 */
    background-position: center;
    animation: spin 2s linear infinite;
    /* 应用旋转动画 */
    left: 50%;
    transform: translate(-50%);
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}