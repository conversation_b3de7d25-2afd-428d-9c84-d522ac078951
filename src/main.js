import Vue from 'vue'
import store from './store';
import App from './App.vue'
import router from './router'
import api from './utils/api'
import lowcodelib from 'lowcodelib';
import components from './components/index.js'
import utils from './utils'
import ElementUI from 'element-ui'
// import 'element-ui/lib/theme-chalk/index.css';
import './element-variables.scss'
import config from './config'
import upEvent from './utils/upEvent.js';

// import VConsole from 'vconsole';
Vue.use(ElementUI, { size: 'small', zIndex: 3000 })
Vue.use(api)
Vue.config.productionTip = false
const CONFIG = {
    BASE_URL: config.BASE_URL, //为了调用接口判断环境
    API_Header: { //为了设置接口请求头
        'X-Tenant-Id': config.TENANT_ID,
        'X-Ext-Tenant-Id': config.EXT_TENANT_ID
    }
}
Vue.use(lowcodelib, CONFIG)
Vue.use(components)
Vue.use(upEvent)

// 只在开发环境下初始化vConsole
// if (process.env.VUE_APP_ENV === 'local') {
//     const vConsole = new VConsole();
//     console.log('VConsole已启用');
// }


Vue.prototype.$myUtils = utils

Vue.prototype.$goLogin = function () {
    const { fullPath, path } = window.vm.$route
    if (path === '/login') {
        return
    }
    window.$router.push({ path: '/login', query: { backUrl: encodeURIComponent(fullPath) } })
}
Vue.prototype.$logout = function () {
    const { fullPath, path } = window.vm.$route
    if (path === '/login') {
        return
    }
    utils.removeCookie('LRToken')  // 移除oa单点登录
    utils.removeCookie()
    window.$router.push({ path: '/login', query: { backUrl: encodeURIComponent(fullPath) } })
}




// 检查url 如果url上面有参数url,直接跳到url上面去
const checkAndRedirectUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectUrl = urlParams.get('url');
    if (redirectUrl) {
        // 解码 URL 并跳转
        const decodedUrl = decodeURIComponent(redirectUrl);
        window.location.href = decodedUrl;
    }
}

// 执行 URL 检查
checkAndRedirectUrl();

window.vm = new Vue({
    store,
    router,
    render: h => h(App),
}).$mount('#app')

