<template>
    <div class="cp-user-header">
        <el-dropdown placement="top-start">
            <img :src="userInfo.avatarUrl" alt="" srcset="">
            <!-- <i class="iconfont icon-shezhi"></i> -->
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(it, i) in userMenu" :key="i" @click.native="it.call">
                    <i class="iconfont" :class="it.icon"></i>
                    <span>{{ it.label }}</span>
                </el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
</template>

<script>
export default {
    name: 'cp-user-header',
    props: {
        title: {}
    },
    computed: {
        isProduction() {
            return process.env.VUE_APP_MODE === 'production'
        },
        userMenu() {
            const that = this
            return [
                {
                    label: '个人中心',
                    icon: 'icon-gerenzhongxin1',
                    call() {
                        that.$router.push({ name: 'userCenter' })
                    }
                },
                {
                    label: '退出登录',
                    icon: 'icon-tuichu1',
                    hide: !!window.tt && that.isProduction,
                    call() {
                        that.$logout()
                    }
                }
            ].filter(it => it.hide !== true)
        },
        userInfo() {
            return this.$store.state.userInfo
        }
    },
    methods: {
    }
}
</script>

<style lang="less" scoped>
.cp-user-header {
    cursor: pointer;

    img {
        width: 30px;
        height: 30px;
        cursor: pointer;
        border-radius: 100vw;
    }

    // .icon-shezhi {
    //     font-size: 24px;
    //     color: #6e768c;
    // }
}
</style>