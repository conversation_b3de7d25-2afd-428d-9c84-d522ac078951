import imagePreview from './imagePreview.vue'

const install = vm => {
    const ImagePreviewConstructor = vm.extend(imagePreview)
    vm.prototype.$preview = function (option, previwList) {
        if (!option || typeof option !== 'string') return false
        const instance = new ImagePreviewConstructor({
            el: document.createElement('div')
        })
        instance.onClose = () => {
            instance.$destroy()
        }
        if (previwList) {
            instance.urlList = previwList
            instance.index = previwList.indexOf(option)
        } else {
            instance.urlList = [option]
        }
    }
}

export default {
    install
}
