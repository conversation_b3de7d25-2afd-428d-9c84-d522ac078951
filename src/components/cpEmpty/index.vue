<template>
    <div class="cp-page-empty">
        <div class="label">{{ title }}</div>
        <div class="center">
            <img src="../../assets/Vector.png" alt="" srcset="">
            <div class="text">功能正在开发中，敬请期待～</div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'cpPageEmpty',
    props: {
        title: {}
    },
    computed: {
    },
    methods: {
    }
}
</script>

<style lang="less">
.cp-page-empty {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .label {
        color: #FFF;
        font-size: 24px;
        font-weight: 900;
        margin-top: 0px;
        margin-left: 0px;
    }

    .center {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;


        img {
            width: 210px;
            height: 245px;
        }

        .text {
            margin-top: 40px; 
            font-size: 16px;
        }
    }
}
</style>