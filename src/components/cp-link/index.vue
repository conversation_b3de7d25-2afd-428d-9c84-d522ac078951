<template>
    <el-button class="cp-link-btn" type="text" @click="handlerClick">{{text}}</el-button>
</template>


<script>
import { goFeishuUser } from '@/utils/feishu.js'

export default {
    name: 'cp-link',
    props: {
        text: {},
        openId: {}
    },
    methods: {
        handlerClick() { 
            if (this.openId) { 
                goFeishuUser(this.openId)
            }
        }
    }
}
</script>


<style lang="less" scoped>
.cp-link-btn{
    font-size: 14px;
    text-decoration: underline;
}
</style>