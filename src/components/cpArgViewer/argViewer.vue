<template>
  <div class="cp-arg-viewer" v-if="list">
    <div class="box-wrap">
      <i class="wrap-icon iconfont icon-zuo viewleft" v-if="canGoleft" @click="goLeft"></i>
      <div class="box">
        <div class="left">
          <video v-if="activeSrc.endsWith('.mp4')" :src="activeSrc" controls></video>
          <el-image v-else :src="$myUtils.generateOssImageUrl(activeSrc, 800, 800)" fit="contain"
            :preview-src-list="[activeSrc]"></el-image>
        </div>
        <div class="right">
          <div class="row1">
            {{ activeItem.workflowName }}
          </div>
          <div class="row2" :key="activeItem.id">
            
            <div class="arg-item-wrap" v-for="(it, i) in activeArg" :key="i">
              <div class="arg-item-prompt" v-if="it.bizType === 'PROMPT_TEXT'">
                <div class="col1">
                  <span>{{ it.keyName }}</span>
                  <i class="iconfont icon-fuzhitishici" @click="copyText(it.value)">复制提示词</i>
                </div>
                <div class="col2">{{ it.value }}</div>
              </div>
              <div class="arg-item" v-else>
                <span class="label">{{ it.keyName }}</span>
                <div class="value mask" v-if="it.bizType === 'MASK_IMAGE'">
                  <el-image :src="getMaskImg(it.value,0,true)" :preview-src-list="[getMaskImg(it.value,0)]"></el-image>
                  <el-image :src="getMaskImg(it.value,1,true)" :preview-src-list="[getMaskImg(it.value,1)]"></el-image>
                </div>
                <audio controls v-else-if="isAudio(it)">
                  <source :src="it.value" type="audio/mpeg">
                </audio>
                <el-image class="value" v-else-if="isImage(it)" :src="$myUtils.generateOssImageUrl(it.value, 80, 80)"
                  :preview-src-list="[it.value]"></el-image>
                <span class="value" v-else>{{ it.value }}</span>
              </div>
            </div>
          </div>
          <div class="row3">
            <i class="btn iconfont icon-fuzhicanshu" @click="reBuildArg">一键同款</i>
            <div class="right-icon">
              <i class="iconfont icon-xiazai1" @click="downImg"></i>
              <i class="iconfont icon-shanchu" v-if="model !== 'readonly'" @click="delImg"></i>
            </div>
          </div>
        </div>
      </div>
      <i class="wrap-icon iconfont icon-you viewright" v-if="canGoRight" @click="goRight"></i>
      <i class="wrap-icon iconfont icon-guanbi1 viewclose" @click="close"></i>

    </div>
  </div>
</template>

<script>

export default {
  props: {
    list: {
      type: Array
    },
    i: {
      type: Number,
      default: 0,
    },
    j: {
      type: Number,
      default: 0,
    },
    $router: {
      type: Object
    },
    copyHis: {
      type: Function
    },
    delHis: {
      type: Function
    },
    model: {
      type: String
    },
  },
  data() {
    return {
      dialogVisible: true
    };
  },
  methods: {
    getMaskImg(value,i,isZip) { 
      const src = value.split(',')[i]
      if (isZip) { 
        return this.$myUtils.generateOssImageUrl(src,80,80) 
      }
      return src
    },
    delImg() {
      this.close()
      this.delHis(this.activeItem)
    },
    close() {
      this.onClose()
    },
    goLeft() {
      if (this.j === 0) {
        if (this.i === 0) {
          return
        }
        this.i--
      } else {
        this.j--
      }
    },
    goRight() {
      if (this.j === this.activeItem.outputImageUrls.length - 1) {
        if (this.i === this.list.length - 1) {
          return
        }
        this.i++
        this.j = 0
      } else {
        this.j++
      }
    },
    isImage(it) {
      return it.value.startsWith('http')
    },
    isAudio(it) {
      return ['.mp3', '.wav', '.aac', '.flac', '.wma', '.ogg'].filter(type => it.value.endsWith(type)).length > 0
    },
    copyText(text) {
      this.$myUtils.copyText(text)
      this.$message.success('提示词复制成功!')
    },
    reBuildArg() {
      this.close()
      if (this.copyHis) {
        this.copyHis(this.activeItem.userInputs)
        return
      }
      this.$router.push({
        name: 'workflowBuild',
        query: {
          id: this.activeItem.workflowId
        },
        params: {
          userInputs: this.activeItem.userInputs
        }
      })
    },
    downImg() {
      const imageUrl = this.activeSrc
      this.$myUtils.downImg(imageUrl)
    },
  },
  computed: {
    canGoleft() {
      return this.j !== 0 || this.i !== 0
    },
    canGoRight() {
      return this.j !== this.activeItem.outputImageUrls.length - 1 || this.i !== this.list.length - 1
    },
    activeItem() {
      return this.list[this.i];
    },
    activeSrc() {
      return this.activeItem.outputImageUrls[this.j];
    },
    activeArg() {
      return this.activeItem.userInputs
    }
  },
  mounted() {
    document.body.appendChild(this.$el);
  },
  destroyed() {
    this.$el.parentNode.removeChild(this.$el);
  }
}
</script>

<style lang="less">
.cp-arg-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.65);

  .box-wrap {
    position: relative;

    .wrap-icon {
      position: absolute;
      border: 2px solid rgba(255, 255, 255, 1);
      border-radius: 200px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 24px;
      box-sizing: border-box;
      cursor: pointer;

      &.viewleft {
        top: 50%;
        left: -70px;
      }

      &.viewright {
        top: 50%;
        right: -70px;
      }

      &.viewclose {
        width: 34px;
        height: 34px;
        top: 4px;
        right: -50px;
      }
    }

    .box {
      width: 1067px;
      height: 546px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 16px;
      overflow: hidden;
      display: flex;
 


      .left {
        background: #fff;
        border-right: 1px solid #E6E8EB;
        width: 680px;
        padding: 24px;

        .el-image {
          width: 100%;
          height: 100%;
        }

        video {
          width: 100%;
          height: 100%;
        }
      }

      .right {
        background: #fff;
        flex: 1;
        padding: 24px;
        height: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;

        .row1 {
          display: flex;
          gap: 24px;
          align-items: center;
          border-bottom: 1px solid #e6e8eb;
          padding-bottom: 12px;
          margin-bottom: 12px;
          font-size: 16px;
          font-weight: bold;
        }

        .row2 {
          display: flex;
          flex-direction: column;
          gap: 8px;
          overflow: auto;
          height:422px;

          .arg-item {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            font-size: 14px;
            gap: 12px;

            .label {
              color: #6E768C;
              white-space: nowrap;
            }

            audio {
              width: 180px;
              height: 32px; 
            }

            .value {
              font-size: 14px;   
              text-align: right;
              &.el-image {
                width: 32px;
                height: 32px;
                border-radius: 4px;
              }
              &.mask{
                display: flex;
                gap:8px;
                .el-image{
                  width: 32px;
                  height: 32px;
                  border-radius: 4px;
                }
              }
            }
          }

          .arg-item-prompt {
            display: flex;
            flex-direction: column;
            gap: 8px;
            font-size: 14px;
            margin-bottom: 12px;

            .col1 {
              display: flex;
              gap: 8px;
              align-items: center;
              justify-content: space-between;
              color: #6E768C;

              i {
                font-size: 14px;
                cursor: pointer;

                &::before {
                  margin-right: 4px;
                }
              }
            }

            .col2 {
              background: #F3F5F8;
              padding: 12px;
              border-radius: 4px;
            }
          }
        }

        .row3 {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0;

          i {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 34px;
            padding: 0px 20px;
            background: #f3f5f8;
            border-radius: 1000px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            gap:8px;
           
          }

          .right-icon {
            display: flex;
            gap: 12px;
            align-items: center;

            .iconfont {
              display: flex;
              align-items: center;
              justify-items: center;
              width: 34px;
              height: 34px;
              box-sizing: border-box;
              padding:0;
            }

            .icon-shanchu {
              color: #fff;
              background-color: #F56C6C;
            }
          }
        }
      }
    }
  }
}
</style>