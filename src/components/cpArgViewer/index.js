import argViewer from './argViewer.vue'

const install = vm => {
    const ImagePreviewConstructor = vm.extend(argViewer)
    vm.prototype.$previewArg = function (i, j, list, { copyHis, delHis, model }) {
        const instance = new ImagePreviewConstructor({
            el: document.createElement('div')
        })
        instance.onClose = () => {
            instance.$destroy()
        }

        const item = list[i]
        instance.list = list.filter(it => it.outputImageUrls && it.outputImageUrls.length)
        const index = instance.list.findIndex(it => it === item)
        instance.i = index
        instance.j = j
        instance.copyHis = copyHis
        instance.delHis = delHis
        instance.model = model

        instance.$router = this.$router
    }
}

export default {
    install
}
