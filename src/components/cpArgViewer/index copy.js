import argViewer from '../cpImageViewer/imagePreview.vue'

const install = vm => {
    const ImagePreviewConstructor = vm.extend(argViewer)
    vm.prototype.$previewArg = function (i, j, list, { copyHis, delHis }) {
        const instance = new ImagePreviewConstructor({
            el: document.createElement('div')
        })
        instance.onClose = () => {
            instance.$destroy()
        }
        const url = list[i].outputImageUrls[j]
        instance.urlList = list.filter(it => it.outputImageUrls && it.outputImageUrls.length).map(it => it.outputImageUrls).flat()
        const index = instance.urlList.findIndex(it => it === url)
        instance.index = index
    }
}

export default {
    install
}
