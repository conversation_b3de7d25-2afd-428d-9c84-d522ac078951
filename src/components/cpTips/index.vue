<template>
    <div class="cp-tips">
        <el-popover placement="right" title="" width="auto" trigger="hover" :content="text">
            <i slot="reference" class="iconfont icon-wenhao"></i>
        </el-popover>
    </div>
</template>

<script>
export default {
    props: {
        text: {}
    },
    name: 'cp-tips',
    computed: {
    },
    methods: {
    }
}
</script>

<style lang="less">
.cp-tips {
    i {
        cursor: pointer;
        font-size: 14px;
        color: #fff;
        line-height: 1;
    }
}
</style>