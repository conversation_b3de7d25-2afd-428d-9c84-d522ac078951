<template>
    <div class="cp-header">
        <div class="left" @click="goBack">
            <i class="el-icon-arrow-left"></i>
            <div class="title">{{ title }}</div>
        </div>
        <div class="right">
            <div class="help-wiki" @click="openHelp" v-if="helpWiki">
                <i class="iconfont icon-jiaocheng"></i>
                <span>使用教程</span>
            </div>
            <cp-user-header />
        </div>
    </div>
</template>

<script>
export default {
    name: 'cp-header',
    props: {
        title: {},
        handerBack: {},
        helpWiki: {}
    },
    computed: {
    },
    methods: {
        openHelp() {
            window.open(this.helpWiki)
        },
        goBack() {
            if (this.handerBack) {
                this.handerBack()
                return
            }
            this.$router.go(-1)
        }
    }
}
</script>

<style lang="less">
.cp-header {
    box-sizing: border-box;
    display: flex;
    height: 56px;
    padding: 8px 24px 8px 26px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E6E8EB;
    backdrop-filter: blur(2px);

    .left {
        display: flex;
        align-items: center;

        i {
            border-radius: 8px;
            display: flex;
            width: 40px;
            height: 40px;
            justify-content: center;
            align-items: center;
            box-sizing: border-box;
            font-size: 14px;
            margin-right: 16px;
            cursor: pointer;
            border: 1px solid #E6E8EB;

            &:hover {
                color: #00b55b;
                border-color: #b3e9ce;
                background-color: #e6f8ef;
            }

        }

        .title {
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }
    }

    .right {
        display: flex;

        &>.help-wiki {

            cursor: pointer;
            height: 32px;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            border-radius: 100px;
            background: rgba(255, 255, 255, 0.09);
            display: inline-flex;
            padding: 0 13px;
            align-items: center;
            margin-right: 13px;
            line-height: 1;
            height: 32px;

            &:hover {
                background: rgba(255, 255, 255, 0.18);
            }

            i {
                margin-right: 10px;
                font-size: 16px
            }
        }
    }
}
</style>