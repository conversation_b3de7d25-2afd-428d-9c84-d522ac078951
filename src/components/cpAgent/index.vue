<template>
    <Render v-if="isShow" :config="config">
        <template slot-scope="renderScope">
            <slot v-bind="renderScope"></slot>
        </template>
    </Render>
</template>


<script>
import Render from './render.vue'
export default {
    name: 'cp-agent',
    props: {
        config: {}
    },
    components: {
        Render
    },
    data() { 
        return {
            isShow:true
        }
    },
    watch: {
        '$route.query.conversationId': {
            handler(newVal, oldValue) {
                if (this.$route.query.nt !== '1') {  // 回复消息的不要刷新页面
                    this.flash()
                    this.$store.commit('initSceneFeatureSwitches')
                }
            },
            immediate: true
        }
    },
    methods: {
        flash() { 
            this.isShow = false
            this.$nextTick(() => {
                this.isShow = true
            })
        }
    }
}
</script>