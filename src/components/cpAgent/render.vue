<template>
    <div class="cp-agent">
        <transition name="slide-fade">
            <div class="left" v-show="showLeft">
                <slot :$goAgent="goAgent"></slot>
            </div>
        </transition>
        <div class="hide-btn" @click="showLeft = !showLeft" :style="{ left: btnLeft + 'px' }">
            <i :class="{ 'el-icon-arrow-left': showLeft, 'el-icon-arrow-right': !showLeft }"></i>
        </div>
        <div class="right">
            <chat ref="chat" v-bind="config"/>
        </div>
    </div>
</template>

<script>
import chat from '@/pages/chat/render.vue'
export default {
    props: {
        config: {}
    },
    components: {
        chat
    },
    data() {
        return {
            showLeft: true
        }
    },
    computed: {
        btnLeft() {
            return this.showLeft ? 399 : 0;
        }
    },
    methods: {
        goAgent(skill) {
            this.$refs.chat.$goAgent(skill)
        }
    }
}
</script>

<style lang="less" scoped>
.cp-agent {
    width: 100%;
    height: 100%;
    display: flex;
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    box-sizing: border-box;
    
    .left {
        flex: 0 0 400px;
        width: 400px;
        transition: all 0.3s ease;
        border-right: 1px solid #E6E8EB;
        box-sizing: border-box;
    }
    
    .hide-btn {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 100px;
        background: #fff;
        border-radius: 0 8px 8px 0;
        border: 1px solid #E6E8EB;
        border-left: 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 6px 0 12px rgba(0,0,0,.08);
        z-index: 1;
        transition: left 0.3s ease;
        
        i {
            font-size: 16px;
            color: #606266;
        }
    }
    
    .right {
        flex: 1;
    }
}

// 添加过渡动画
.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
    transform: translateX(-100%);
    opacity: 0;
}
</style>