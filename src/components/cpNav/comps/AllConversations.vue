<!-- 全部对话 -->
<template>
  <div class="all-conversations" v-if="drawer">
    <el-dialog :visible.sync="drawer">
      <template #title>
        <div class="dialog-title">
          <span>管理对话记录，共{{ total }}条</span>
          <el-input
            placeholder="搜索历史记录"
            @change="handleSearchChange"
            v-model="searchValue"
            class="search-input"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>
      </template>

      <el-table
        :key="tableKey"
        ref="multipleTable"
        :data="list"
        style="width: 100%"
        :height="400"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column property="name" label="对话名称">
          <template slot-scope="scope">
            <span class="name-link" @click="detail(scope.row)">{{
              scope.row.name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column property="changeDate" label="最近一次对话时间">
          <template slot-scope="scope">
            <span class="time">{{
              time(scope.row.changeDate)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <i
              class="iconfont icon-shanchulie"
              @click="handleDelete(scope.row)"
            ></i>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <!-- <el-checkbox v-model="checked" @change="selectAll">全选</el-checkbox> -->
        <div class="right">
          <el-button @click="drawer = false">取 消</el-button>
          <el-button type="danger" @click="deleteByIds">删除所选</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import utils from "@/utils/index.js";
export default {
  data() {
    return {
      tableKey:0,
      checked: "",
      searchValue: "",
      drawer: false,
      list: [],
      multipleSelection: [],
      pageSize: 500,
      pageNo: 1,
      total: 0,
      tableHeight: "100%",
    };
  },
  watch: {
    drawer(val) {
      if (val) {
        this.checked = "";
        this.searchValue = ""
        this.getList();
      }
    },
    multipleSelection(value){
      if(value.length ===this.list.length){
        this.checked = true
      }else{
        this.checked = false
      }
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    time(t){
      return utils.formatTimestamp(t)
    },
    // 单个删除
    async handleDelete(row) {
      try {
        await this.$confirm("删除后不可恢复，确定要删除吗？", "删除会话", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        });
        await this.$api.post("/ai-main-app/api/conversation/delete", {
          id: row.id,
        });
        this.getList();
        this.$store.dispatch("initList");
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        // 判断当前删除的会话是否是当前正在查看的会话，且当前页面是chat页面
        const currentConversationId = this.$route.query.conversationId;
        if (
          this.$route.path === "/chat" &&
          currentConversationId &&
          currentConversationId === this.item.id
        ) {
          this.$router.push("/chat");
        }
        this.getList();
      } catch (error) {
        console.error("Error deleting:", error);
      }
    },
    // 批量删除
    deleteByIds() {
      const ids = this.multipleSelection.map((item) => {
        return item.id;
      });
      if (!ids.length) {
        this.$message({
          type: "error",
          message: "请先先选择对话再删除",
        });
        return;
      }
      this.$confirm("删除后，该对话将不可恢复。确认删除吗？", "永久删除对话", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await this.$api.post("/ai-main-app/api/conversation/deleteByIds", {
            ids,
          });
          this.getList();
          this.$store.dispatch("initList");
          this.$message({
            type: "success",
            message: "删除成功!",
          });
          // 判断当前删除的会话是否是当前正在查看的会话，且当前页面是chat页面
          const currentConversationId = this.$route.query.conversationId;
          if (
            this.$route.path === "/chat" &&
            currentConversationId &&
            ids.includes(currentConversationId)
          ) {
            this.$router.push("/chat");
          }
        })
        .catch(() => {});
    },
    // 搜索
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    selectAll() {
      this.$refs.multipleTable.toggleAllSelection();
    },
    handleSearchChange:utils.debounce(function () {
      this.getList();
    },300),
    detail(row) {
      this.$router.push({ path: `/chat`, query: { conversationId: row.id } });
      this.drawer = false;
    },

    async getList() {
      const res = await this.$api.post(
        "/ai-main-app/api/conversation/listMyPage",
        {
          data: { nameLike: this.searchValue },
          pageNo: this.pageNo,
          pageSize: this.pageSize,
        }
      );
      this.list = res.list;
      this.total = res.total;
      this.tableKey = this.tableKey + 1
    },
  },
};
</script>

<style lang="less" scoped>
@import url(../../../glob.less);

.all-conversations {
  .dialog-title {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    flex-wrap: nowrap;
  }
  .search-input {
    width: 300px;
    margin: 0 20px;
    // flex-grow: 1;
  }

  .btn-wrap {
    margin-left: 10px;
  }
  .iconfont {
    cursor: pointer;
  }
  /deep/ .el-drawer__body {
    padding-bottom: 70px;
  }
  .pagination-wrap {
    position: absolute;
    left: 10px;
    bottom: 30px;
    z-index: 10000;
  }
  /deep/.el-table {
  padding: 0 8px !important;
}
}
/deep/.el-dialog {
  margin-top: 0 !important;
}
/deep/.el-dialog__header {
  // background: #F3F5F8;
  // padding:  12px 10px !important;
  border-radius: 16px 16px 0 0;
}
/deep/.el-dialog__body {
  border-radius: 16px;
}
/deep/.dialog-footer {
  display: flex !important;
  width: 100%;
  // justify-content: space-between !important;
  align-items: center;
}
.name-link {
  color: #222222;
  cursor: pointer;
}
/deep/ .el-input--small .el-input__inner {
  border-radius: 20px;
}
/deep/ .el-button--small,
.el-button--mini {
  border-radius: 20px;
}

</style>