<template>
  <div class="demand-drawer">
    <el-drawer
      title="意见反馈"
      :visible.sync="show"
      direction="rtl"
      size="678px"
      custom-class="el-drawer-demand-drawer"
    >
      <div class="qiankun-demand-drawer-content">
        <div class="row1">
          <img
            src="https://oss.syounggroup.com/bigfile/defaultTenantId/demandEmoji.png"
            alt=""
            srcset=""
          />
          <span class="text">恭喜，你发现了一个少有人知的宝藏功能！</span>
        </div>
        <div class="row2">
          <div>您可以在这里反馈，在使用过程中遇到的问题与障碍。</div>
          <div>也可以提出对系统设计的建议，或者想要的新功能。</div>
          <div>
            数字中心的羊羊会珍惜你的意见，<span class="bold">安排专人处理</span
            >，为你做出改进。
          </div>
        </div>
        <div class="row3">
          <div class="col1">
            反馈内容
            <i class="hermes hermes-sijiaoxing"></i>
          </div>
          <el-input
            type="textarea"
            :rows="6"
            resize="none"
            placeholder="请输入内容"
            v-model="message"
          >
          </el-input>
        </div>
        <div class="row4">
          <sy-upload
            v-model="uploadImgs"
            v-bind="upload"
          />
        </div>
        <div class="row5">
          <el-button type="primary" @click="submit">提交</el-button>
        </div>
        <div class="row6">
          <div class="col1">提交记录（近3条）</div>
          <div class="cols" v-for="(it, i) in list" :key="i">
            <span class="time">{{
              $myUtils.timeFormate(it.createDate, "yyyy-MM-dd hh:mm")
            }}</span>
            <span class="system-name">{{
              it.systemName.replace("HERMES-", "")
            }}</span>
            <span class="text" :title="it.content">{{ it.content }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { create } from "sortablejs";
import config from '../../../config'
export default {
  data() {
    return {
      show: false,
      message: "",
      uploadImgs: [],
      list: [],
    };
  },
  computed: {
    upload() {
      return {
        paste: true,
        uploadText:
          "<div>鼠标悬停到这个区域后</div><div>按Control+V粘贴图片</div><div>或者拖拽图片到这个区域</div>",
        iconClass: "el-icon-upload",
        type: "image",
        bind: {
          drag: true,
          limit: 4,
        },
      };
    },
    userInfo() {
      return this.$store.state.userInfo;
    },
  },
  created() {
    this.getList();
  },
  methods: {
    async httpRequest(data) {
      const file = data.file
      const formData = new FormData()
      formData.append('file', file)
      formData.append(
        'group',
        config.TENANT_ID
      )
      let res
      try {
        res = await this.$uploadBigFile(formData, data)
      } catch (error) {
        if (error.name === 'cancel') {
          this.$message.error('上传取消')
        } else {
          this.$message.error(`上传失败，${JSON.stringify(error)}`)
        }
        throw new Error(error.message)
      }
      return res
    },
    open() {
      this.show = true;
    },
    async submit() {
      if (!this.message) {
        this.$message({
          message: "请输入反馈内容",
          type: "warning",
        });
        return;
      }
      await this.$api.post("/app-service/api/feedback/create", {
        fileUrls: this.uploadImgs.map((it) => it.url),
        content: this.message,
        great: 0,
        name: this.userInfo.name,
        system: "zeus",
      });
      this.$message({
        message: "提交成功",
        type: "success",
      });
      this.message = "";
      this.uploadImgs = [];
      this.getList();
      this.show = false;
    },
    async getList() {
      const res = await this.$api.post("/app-service/api/feedback/lastFeedback", null, {
        params: { name: this.userInfo.name },
      });
      this.list = res;
    },
  },
};
</script>

<style lang="less">
.demand-drawer {
  text-align: left;
  font-family: "PingFang SC";
  .el-drawer-demand-drawer {
    overflow: auto;
  }

  .el-drawer__header {
    text-align: left;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    
    border-bottom: 1px solid #f3f5f8;
    margin-bottom: 0;
    padding-bottom: 11px;
    padding-top: 11px;

    .el-icon-close {
      color: rgba(158, 164, 178, 1);
    }
  }

  .qiankun-demand-drawer-content {
    padding: 0 30px;
    min-height: 784px;
    position: relative;

    .row1 {
      display: flex;
      align-items: center;
      margin-top: 16px;

      img {
        width: 26px;
        margin-right: 10px;
      }

      .text {
        
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
      }
    }

    .row2 {
      text-align: left;
      margin-top: 22px;
      padding: 10px 0 10px 20px;
      display: flex;
      flex-direction: column;
      border-radius: 4px;
      background: #E0F2E9;
      
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;

      .bold {
        font-weight: 600;
      }
    }

    .row3 {
      margin-top: 22px;

      .col1 {
        width: 100px;
        height: 30px;
        border-radius: 20px 0px 0px 0px;
        background: linear-gradient(
          90deg,
          #00B55B 0%,
          #E0F2E9 46.88%,
          #fff 95.68%
        );
        
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        padding-left: 20px;
        display: flex;
        align-items: center;

        .hermes-sijiaoxing {
          font-size: 10px;
          position: relative;
          top: -4px;
          left: 4px;
        }
      }

      .el-textarea {
        .el-textarea__inner {
          color:#0D1B3F;
          border-top-left-radius: 0;
        }
      }
    }

    .row4 {
      margin-top: 10px;

      .sy-upload-box {
        white-space: normal;
        width: 628px;

        .el-upload-list li:last-child {
        }
      }

      .el-upload--picture-card {
      }

      .upload-btn {
        .el-icon-upload {
          font-size: 32px;
          margin-top: 18px;
          margin-bottom: 6px;
          line-height: 1;
          color: rgba(192, 196, 204, 1);
        }

        .upload-text-wrap {
          color: #909399;
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          font-size: 12px;
        }
      }
    }

    .row5 {
      margin-top: 10px;

      .el-button {
        width: 120px;
      }
    }

    .row6 {
      position: absolute;
      bottom: 0;
      width: auto;
      color: #6e768c;
      font-size: 12px;
      width: calc(100% - 60px);
      box-sizing: border-box;
      padding-bottom: 6px;

      .col1 {
        padding: 8px 0;
        border-bottom: 1px solid var(--Gary-Gary-4, #e6e8eb);
        margin-bottom: 4px;
      }

      .cols {
        padding: 6px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        .system-name {
          display: inline-block;
          width: 60px;
          margin-left: 18px;
        }

        .text {
          margin-left: 18px;
        }
      }
    }
  }
}
</style>
