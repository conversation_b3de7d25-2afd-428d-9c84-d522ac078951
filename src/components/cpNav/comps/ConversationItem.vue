<template>
    <div class="item" @click="goToConversation" :class="{
        active: isActive,
        renameFocus: isRenaming,
        'is-app': isApp
    }">
        <!-- 重命名input -->
        <el-input class="rename-input" @change="handleNameChange" @blur="handleNameChange" v-if="isRenaming"
            v-model="newName" placeholder="请输入内容"></el-input>
        <div v-else class="text" :title="item.name">
            <span class="chat-type">
                <i class="iconfont" :class="[isApp ? 'icon-a-yingyongyingyongguanli' : 'icon-huihua1']"></i>
                <span v-if="isApp">智能体</span>
            </span>
            <span class="name">{{ item.name }}</span>
        </div>
        <div class="edit">
            <Edit :item="item" :isPinned="isPinned" @update="$emit('update')" @rename="startRename" />
        </div>
    </div>
</template>

<script>
import Edit from './Edit.vue'

export default {
    name: 'ConversationItem',
    components: {
        Edit
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        isPinned: {
            type: Boolean,
            default: false
        },
        isActive: {
            type: Boolean,
            default: false
        },
        isRenaming: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            newName: this.item.name
        }
    },
    computed: {
        isApp() {
            return this.item.appCode !== 'ZEUS_CHAT'
        }
    },
    methods: {
        goToConversation() {
            this.$emit('click', this.item)
        },
        handleNameChange(value) {
            this.$emit('name-change', value)
        },
        startRename(id) {
            this.$emit('rename', id)
        }
    }
}
</script>

<style lang="less" scoped>
.item {
    width: 100%;
    padding: 0 12px;
    height: 36px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
    margin-top: 2px;
    border: 1px solid transparent;
    transition: all 0.3s ease;

    &:hover {
        border-radius: 8px;
        background: rgb(233, 234, 236);

        .edit {
            display: block;
        }
    }

    &.active {
        border-radius: 8px;
        background: #ffffff;
    }

    &.is-app {
        .text {
            .chat-type {
                border-radius: 2px;
                background-color: #E0F2E9;
                color: #00B55B;

                i {
                    color: #00B55B;
                }


            }

            .name {
                width: calc(100% - 62px);
            }
        }

    }

    &.renameFocus {
        padding: 0;
    }

    .text {
        font-size: 14px;
        white-space: nowrap;
        flex-grow: 1;
        width: calc(100% - 40px);
        display: flex;
        align-items: center;
        gap: 4px;

        .chat-type {
            font-size: 12px;
            line-height: 14px;
            font-weight: normal;
            display: flex;
            gap: 4px;
            align-items: center;
            padding: 2px 4px;

            i {
                font-size: 14px;
                color: #B6BAC5;
            }

        }


        .name {
            width: calc(100% - 16px);
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .rename-input {
        width: 100%;
        height: 100%;
        background: transparent;
        color: inherit;
        padding: 0;
        box-sizing: border-box;
        border-radius: 14px;

        input {
            height: 100%;
            border-radius: 14px;
            border-color: #00B55B !important;
        }
    }

    .edit {
        display: none;
    }
}
</style>