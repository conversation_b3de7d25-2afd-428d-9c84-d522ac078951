<template>
  <div class="edit-actions">
    <el-dropdown placement="bottom-start">
      <i class="icon iconfont icon-sangedian"></i>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item @click.native="handlePin">
          <i class="iconfont" :class="isPinned ? 'icon-quxiaozhiding' : 'icon-zhiding'"></i>
          <span class="text">{{ isPinned ? "取消置顶" : "置顶" }}</span>
        </el-dropdown-item>
        <el-dropdown-item @click.native="emitRename">
          <i class="iconfont icon-bianji1"></i>
          <span class="text">重命名</span>
        </el-dropdown-item>
        <el-dropdown-item @click.native="handleDelete">
          <i class="iconfont icon-shanchulie"></i>
          <span class="text">删除</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
import { mapState } from "vuex";
export default {
  props: {
    item: Object,
    isPinned: Boolean,
  },
  computed: {
    ...mapState(["navInfo"]), // 使用 Vuex 的状态
  },
  methods: {
    async handlePin() {
      try {
        if (this.isPinned) {
          await this.$api.post("/ai-main-app/api/conversation/unPin", {
            id: this.item.id,
          });
        } else {
          await this.$api.post("/ai-main-app/api/conversation/pin", {
            id: this.item.id,
          });
        }

        this.$emit("update"); // Emit an event to update the list
      } catch (error) {
        console.error("Error pinning/unpinning:", error);
      }
    },
    emitRename() {
      this.$emit('rename', this.item.id);
    },
    async handleDelete() {
      try {
        await this.$confirm('删除后不可恢复，确定要删除吗？', '删除会话', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await this.$api.post("/ai-main-app/api/conversation/delete", {
          id: this.item.id,
        });

        // 判断当前删除的会话是否是当前正在查看的会话，且当前页面是chat页面
        const currentConversationId = this.$route.query.conversationId;
        if (this.$route.path === '/chat' && currentConversationId && currentConversationId === this.item.id) {
          this.$router.push('/chat');
        }

        this.$emit("update");
      } catch (error) {
        console.error("Error deleting:", error);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.edit-actions{
  line-height: 22px;
  height: 22px;
}

/deep/ .el-dropdown-menu__item {
  .iconfont { 
    font-size: 16px !important;
  }
  .text {
    font-size: 14px; 
  }
}
</style>