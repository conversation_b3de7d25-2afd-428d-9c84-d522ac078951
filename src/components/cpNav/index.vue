<template>
  <div :class="['cp-nav', { collapsed: isNavCollapsed }]">
    <div class="logo-wap" v-if="!isNavCollapsed">
      <!-- 展开的logo -->
      <div class="left" @click="createChat">
        <img class="logo" src="@/assets/logo/logo-h.png" alt="" srcset="" />
        <!-- <span class="text">ZEUS</span> -->
      </div>

      <i class="iconfont icon-caidanshouqi" @click="toggleNavCollapse"></i>
    </div>
    <div class="content" v-if="!isNavCollapsed">
      <div class="fixed-top">
        <div class="btn-wrap">
          <el-button class="btn" @click="createChat"><i class="el-icon-plus"></i>新对话</el-button>
        </div>
        <div class="nav" :class="{ active: active === '/' + it.name }" v-for="(it, i) in list" :key="i"
          @click="goPage(it)">
          <i class="iconfont" :class="it.icon"></i>
          <span>{{ it.label }}</span>
        </div>
        <div class="line"></div>
      </div>

      <AllConversations ref="allConversations"></AllConversations>
      <div class="scrollable-content hover-scroll " v-if="pinList.length || latest.length">
        <!-- 置顶功能 -->
        <div class="top">
          <div class="title" v-if="pinList.length">
            <div>
              <span>置顶</span>
              <i @click="togglePinList" class="iconfont" :class="isPinListExpanded
                  ? 'icon-a-fangxiangshang'
                  : 'icon-a-fangxiangxia'
                "></i>
            </div>
            <el-tooltip v-if="pinList.length" effect="dark" content="管理全部对话" placement="top-start">
              <i class="iconfont icon-history" @click="openAll"></i>
            </el-tooltip>
          </div>
          <Empty v-if="!pinList.length" v-show="isPinListExpanded"></Empty>
          <template v-else>
            <ConversationItem
              v-for="(item, i) in pinList"
              :key="i"
              :item="item"
              :is-pinned="true"
              :is-active="isActive(item.id)"
              :is-renaming="item.id === activeId"
              v-show="isPinListExpanded"
              @click="goToConversation"
              @name-change="handleSChange"
              @rename="startRename"
              @update="initList"
            />
          </template>
        </div>
        <!-- 最近 -->
        <div class="top">
          <div class="title">
            <span>最近</span>
            <el-tooltip v-if="!pinList.length" effect="dark" content="管理全部对话" placement="top-start">
              <i class="iconfont icon-history" @click="openAll"></i>
            </el-tooltip>
          </div>
          <Empty v-if="!latest.length"></Empty>
          <template v-else>
            <ConversationItem
              v-for="(item, i) in latest"
              :key="i"
              :item="item"
              :is-pinned="false"
              :is-active="isActive(item.id)"
              :is-renaming="item.id === activeId"
              @click="goToConversation"
              @name-change="handleSChange"
              @rename="startRename"
              @update="initList"
            />
          </template>
        </div>
      </div>
      <div class="empty" v-else>
        <img src="@/assets/empty.png" alt="" />
        <div class="text">没有历史对话</div>
        <div class="text">和我聊聊吧</div>
      </div>
      <div class="setting">
        <cp-user-header />
        <!-- <i class="iconfont icon-lingganguangchang"></i><span>提需求</span> -->
        <el-button @click="openDemandDrawer" size="mini"><i class="iconfont icon-jushou"></i>提需求</el-button>
      </div>
    </div>
    <DemandDrawer ref="demandDrawer" v-if="userInfo.name" />
  </div>
</template>

<script>
import AllConversations from "./comps/AllConversations.vue";
import Empty from "./comps/Empty.vue";
import DemandDrawer from "./comps/DemandDrawer.vue";
import ConversationItem from "./comps/ConversationItem.vue";
import { mapState, mapMutations } from "vuex";
export default {
  name: "cp-nav",
  components: {
    AllConversations,
    Empty,
    DemandDrawer,
    ConversationItem
  },
  data() {
    return {
      dialogVisible: false, // 删除提醒是否可见
      activeId: "", // 被重命名选中的会话
      newName: "", // 重命名
      searchValue: "",
      isPinListExpanded: true, // 控制置顶列表的展开状态
    };
  },
  computed: {
    ...mapState(["isNavCollapsed", "pinList", "latest"]), // 使用 Vuex 的状态
    userInfo() {
      return this.$store.state.userInfo;
    },
    active() {
      return this.$route.path;
    },
    list() {
      return [
        {
          label: "AI技能库",
          icon: "icon-AIjinengku",
          name: "aiSkillsLibrary",
        },
        {
          label: "艺境ArtScape",
          icon: "icon-yijing",
          name: "workflowApplication",
        },
        {
          label: "灵感广场",
          icon: "icon-lingganguangchang",
          name: "sourceMaterial",
        },
        // {
        //   label: "AI工具箱",
        //   icon: "icon-gongju",
        //   name: "toolbox",
        // },
        // {
        //     label: '教程/使用手册',
        //     icon: 'icon-jiaocheng',
        //     name: 'help'
        // },
        // {
        //     label: '个人中心',
        //     icon: 'icon-gerenzhongxin',
        //     name: 'userCenter'
        // },
        // {
        //     label: '自播脚本检查',
        //     icon: 'icon-gongzuoliuyingyong',
        //     name: 'liveCheck'
        // },
      ];
    },
  },
  mounted() {
    this.initList();
  },
  watch: { 
    pinList(value) { 
      if (value.length === 0) {
        this.isPinListExpanded = false;
      } else {
        this.isPinListExpanded = true;
      }
    }
  },
  methods: {
    ...mapMutations(["setNavColspan"]), // 使用 Vuex 的 mutation
    toggleNavCollapse() {
      this.setNavColspan(!this.isNavCollapsed); // 更新 Vuex 状态
    },
    openDemandDrawer() {
      this.$refs.demandDrawer.open()
    },
    // 置顶展开收起
    togglePinList() {
      this.isPinListExpanded = !this.isPinListExpanded;
    },
    // // 搜索对话
    // handleSearchChange() {
    //   this.$store.dispatch("initList", this.searchValue);
    // },
    // 名字改变
    async handleSChange(item) {
      if (item && this.activeId && item instanceof String || typeof item === 'string') {
        const currentItem = this.pinList.find((i) => i.id === this.activeId) || this.latest.find((i) => i.id === this.activeId);
        if (currentItem && currentItem.name !== item) {
          if (!item) {
            this.$message.error('不能为空');
            return;
          }
          if (item.length > 50) {
            this.$message.error(`对话标题不能超过50个字符，现有长度${item.length}，请删减`);
            return;
          }
          await this.$api.post("/ai-main-app/api/conversation/rename", {
            id: this.activeId,
            name: item,
          });
          this.initList();
        }
        this.activeId = "";
      } else {
        this.activeId = "";
      }
    },
    startRename(id) {
      const item =
        this.pinList.find((item) => item.id === id) ||
        this.latest.find((item) => item.id === id);
      if (item) {
        this.activeId = id;
        this.newName = item.name;
      }
    },
    // 删除
    async deleteChat(id) {
      this.$confirm("删除后，该对话将不可恢复。确认删除吗？", "永久删除对话", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const res = await this.$api.post(
            "/ai-main-app/api/conversation/delete",
            {
              id,
            }
          );
          this.initList();
          this.$message({
            type: "success",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    // 重命名
    async rename(id, name) {
      this.newName = name;
      this.activeId = id;
    },
    //置顶操作
    async pin(id) {
      await this.$api.post("/ai-main-app/api/conversation/pin", { id });
      this.initList();
    },
    //取消置顶操作
    async unPin(id) {
      await this.$api.post("/ai-main-app/api/conversation/unPin", { id });
      this.initList();
    },
    // 查询我的会话列表（置顶/最近）
    async initList() {
      await this.$store.dispatch("initList", this.searchValue);
    },
    createChat() {
      if (this.$route.query.conversationId || this.$route.path !== "/chat") {
        this.$router.push("/chat");
      }
    },
    isActive(id) {
      return this.$route.query.conversationId === id;
    },
    openAll() {
      this.$refs.allConversations.drawer = true;
    },
    goPage(it) {
      const targetPath = `/${it.name}`;
      if (this.$route.path !== targetPath) {
        this.$router.push(targetPath);
      }
    },
    goToConversation(item) {
      let targetPath = `/chat`;
      const targetQueryId = item.id;
      const currentQueryId = this.$route.query.conversationId;
      if (item.skill?.interactionType === 'APPLICATION_IN') { 
        targetPath = item.skill.value
      }
      if (currentQueryId !== targetQueryId) {
        this.$router.push({
          path: targetPath,
          query: { conversationId: targetQueryId },
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.fixed-top {
  padding: 0px 16px;
  // flex-shrink: 0;
  /* 固定顶部部分不随内容变化 */
}

.scrollable-content {
  flex-grow: 1;
  overflow-y: auto;
  transition: overflow-y 0.3s;
  padding: 0px 8px 0 16px;
  margin-bottom: 50px;
  position: relative;
  /* 确保子元素的sticky效果正常 */
}

.empty {
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #6e768c;

  img {
    width: 30px;
    margin-bottom: 20px;
  }
}

.setting {
  flex-shrink: 0;
  /* 固定底部部分不随内容变化 */
}

.cp-nav {
  height: 100vh;
  overflow: hidden;
  width: 100%;
  /* 确保 .cp-nav 的宽度为 246px */
  //   padding: 0px 24px;
  background: #F3F5F8;
  padding-bottom: 50px;
  box-sizing: border-box;

  &.collapsed {
    width: 40px;
    /* 收起时的宽度 */
    padding: 0 4px;

    .logo {
      display: none;
    }

    .shouqi-logo {
      display: block;
    }

    .btn-wrap,
    .nav,
    .line,
    .top,
    .setting cp-user-header {
      display: none;
      /* 收起时隐藏的元素 */
    }
  }

  .search {
    display: flex;
    margin-bottom: 20px;

    .all-btn:hover {
      background-color: #E0F2E9;
      border-color: #22c3ad;
      color: #22c3ad;
    }

    .all-btn:active {
      background-color: #E0F2E9;
      border-color: #22c3ad;
      color: #22c3ad;
    }

    .all-btn:focus {
      background-color: #E0F2E9;
      border-color: #22c3ad;
      color: #22c3ad;
    }

    > :first-child {
      margin-right: 10px;
    }
  }

  .logo-wap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    cursor: pointer;

    .left {
      display: flex;
      align-items: center;

      .text {
        font-size: 24px;
        font-weight: 600;
        background: linear-gradient(180deg, #7CD5A9 0%, #00B55B 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        letter-spacing: 0;
      }
    }

    .logo {
      width: 110px;
      margin-left: 16px;
      margin-right: 8px;
    }

    .icon-caidanshouqi {
      margin-right: 12px;
    }

    .iconfont {
      cursor: pointer;
      font-size: 22px;
      color: #6e768c;
    }
  }

  .btn-wrap {
    margin-top: 20px;
    margin-bottom: 8px;
    cursor: pointer;

    .btn {
      font-size: 14px;
      width: 100%;
      border-radius: 30px;
      cursor: pointer;
      color: #00B55B;
      height: 36px;
      border-radius: 8px;
      /* 绿色-背景 */
      background: #E0F2E9;
      box-sizing: border-box;
      /* 绿色-主色 */
      border: 1px solid #00B55B;
    }

    .btn:hover {
      background-color: #32c079;
      border-color: #32c079;
      color: #ffffff;
    }
  }

  .line {
    width: 100%;
    height: 1px;
    margin: 8px 0;
    background-color: #e6e8eb;
  }

  .nav {
    width: 100%;
    padding: 0 8px;
    height: 36px; 
    font-size: 14px;
    display: flex;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
    margin-top: 2px;
    border: 1px solid transparent;
    line-height: 16px;

    &:hover {
      border-radius: 8px;
      background: rgb(233, 234, 236);
    }

    &.active {
      span {
        font-weight: bold;
      }

      border-radius: 8px;
      // border: 1px solid #E0F2E9;
      background: #ffffff;
      
    }

    i { 
      margin-right: 10px;
      font-size: 18px;
    }
  }

  .top {
    margin-top: 10px;

    .title {
      position: sticky;
      top: 0;
      /* 让它在滚动到顶部时保持固定 */
      background-color: #F3F5F8;
      /* 给它一个背景色以覆盖滚动内容 */
      z-index: 10;
      /* 确保它在其他内容之上 */
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #9EA4B2;
      margin-bottom: 4px;
      padding: 8px 0px;

      /* 增加一些内边距以更好地显示标题 */
      i {
        cursor: pointer;
        font-size: 12px;
        margin-left: 8px;
      }

      .icon-history {
        font-size: 16px;
      }
    } 
 
  }

  .setting {
    width: 250px;
    height: 50px;
    display: flex;
    position: fixed;
    padding: 0 16px;
    bottom: 0;
    left: 0;
    justify-content: space-between;
    align-items: center;
    background: #F3F5F8;
    border-top: 0.5px solid #e6e8eb;
    box-sizing: border-box;

    .avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
    }

    /deep/.el-button--mini {
      background: #F3F5F8;
      padding: 6px 8px;
      
      font-size: 12px;
      border-radius: 4px;
    }

    /deep/ .el-button:focus {
      border-color: #dcdfe6;
      
    }

    /deep/ .el-button:hover {
      border-color: #00B55B;
      color: #00B55B;
    }

  }
}

/deep/.el-input--small .el-input__inner {
  border-radius: 8px !important;
  height: 36px;
  line-height: 36px;
}

.icon-jushou {
  margin-right: 4px;
}
</style>