<template>
    <div class="cp-loading-text">
        <div class="loading-wrap" v-if="isLoading">
            <img src="@/assets/loading-small.png" alt="">
            <span>{{ loadingText }}</span>
        </div>
        <div class="text-wrap" v-else>
            <span>{{ text }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: 'cp-loading-text',
    props: {
        loadingText: {
            type: String,
            default: '正在生成中...'
        },
        text: {
            type: String,
            default: ''
        },
        isLoading: {
            type: Boolean,
            default: true
        }
    }
}
</script>

<style lang="less">
.cp-loading-text {
    background: rgba(0,0,0, 0.06);
    border-radius: 8px;
    box-sizing: border-box;
    padding: 12px 18px;
    box-sizing: border-box;
    overflow: auto;
    border: 1px solid #323B41;

    .loading-wrap {
        color: #828588;
        font-size: 14px;
        line-height: 16px;
        display: flex;
        align-items: center;
        gap: 8px;

        img {
            width: 16px;
            height: 16px;
            // 转动动画
            animation: rotate 1s linear infinite;

        }
    }

    .text-wrap {
        font-size: 14px;
        line-height: 20px;
        margin-top: 4px;
    }

}
</style>