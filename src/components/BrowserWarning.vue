<template>
  <div class="browser-warning">
    <div class="warning-content">
      <img src="@/assets/logo/竖版LOGO.png" alt="系统Logo" class="system-logo">
      <p>为了获得最佳体验，推荐使用谷歌浏览器访问ZEUS</p>
      <div class="download-links">
        <el-button 
          type="primary" 
          size="large"
          @click="openChromeDownload"
        >
          下载谷歌浏览器
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'BrowserWarning',
  data() {
    return {
    }
  },
  methods: {
    openChromeDownload() {
      window.open('https://www.google.cn/chrome/', '_blank')
    }
  }
}
</script>

<style scoped>
.browser-warning {
  display: flex;
  justify-content: center;
  align-items: center;
}

.warning-content { 
  padding: 48px;
  border-radius: 12px;
  text-align: center; 
}

.system-logo {
  width: 120px;
  height: auto;
  margin-bottom: 24px;
}

.warning-content i {
  font-size: 52px;
  color: #E6A23C;
  margin-bottom: 24px;
}

.warning-content h2 {
  color: #303133;
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 600;
}

.warning-content p {
  color: #606266;
  margin-bottom: 32px;
  font-size: 16px;
  line-height: 1.6;
}

.download-links {
  margin-top: 8px;
}

.download-links .el-button {
  padding: 14px 32px;
  font-size: 16px;
  font-weight: 500;
}
</style> 