export default {
    install(Vue) {
        const importAll = require.context('./', true, /index\.vue$/);
        importAll.keys().forEach(key => {
            const component = importAll(key).default
            Vue.component(component.name, component)
        })


        const importAllJS = require.context('./', true, /index\.js$/);

        importAllJS.keys().forEach(key => {
            if (key === './index.js') {
                return
            }
            const JS = importAllJS(key).default
            Vue.use(JS)
        })
    }
}