<template>
    <div class="">
        <div ref="shadowHost" class="shadow-content"></div>
    </div>
</template>

<script>
export default {
    name: 'cp-shadow',
    props: {
        content: {
            type: String,
            default: ''
        },
        styles: {
            type: String,
            default: `
                table { border-collapse: collapse; margin: 10px 0; }
                td, th { border: 1px solid #ddd; padding: 8px; }
                img { max-width: 100%; height: auto; margin: 10px 0; display: block; }
                p { word-wrap: break-word; overflow-wrap: break-word; }
                pre { white-space: pre-wrap; word-wrap: break-word; overflow-wrap: break-word; }
            `
        }
    },
    mounted() {
        const host = this.$refs.shadowHost;
        if (!host) return;

        // 创建Shadow DOM
        const shadowRoot = host.attachShadow({ mode: 'open' });

        // 添加样式
        const style = document.createElement('style');
        style.textContent = this.styles;

        // 创建内容容器
        const container = document.createElement('div');
        container.innerHTML = this.content;

        // 将样式和内容添加到Shadow DOM
        shadowRoot.appendChild(style);
        shadowRoot.appendChild(container);

        // 保存引用以便后续清理
        this._shadowRoot = shadowRoot;
    },
    beforeDestroy() {
        // 清理 Shadow DOM
        if (this._shadowRoot) {
            // 移除所有子节点
            while (this._shadowRoot.firstChild) {
                this._shadowRoot.removeChild(this._shadowRoot.firstChild);
            }
            this._shadowRoot = null;
        }
    }
}
</script>

<style lang="less" scoped>
.shadow-content {
    width: 100%;
    height: 100%;
}
</style> 
