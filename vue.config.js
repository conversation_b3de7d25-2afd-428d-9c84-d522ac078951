module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  filenameHashing: process.env.VUE_APP_ENV !== 'local',
  configureWebpack: {
    // devtool: 'source-map',
    devtool: process.env.VUE_APP_ENV === 'local' ? false : 'source-map',
    devServer: {
      disableHostCheck: true,
      port: 80,
      https: false
    },
    optimization: {
      minimize: process.env.VUE_APP_ENV !== 'local'
    }
  },
  ...(process.env.VUE_APP_ENV !== 'local' ? {
    pages: {
      index: {
        entry: 'src/main.js',
        template: 'public/index.html',
        filename: 'index.html'
      },
      share: {
        entry: 'src/pages/chat/share/share.js',
        template: 'public/share.html',
        filename: 'share.html'
      }
    }
  } : {})
}
