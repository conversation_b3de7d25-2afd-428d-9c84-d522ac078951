{"name": "zeus-artScape", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode local", "prod": "vue-cli-service serve --mode prod", "build": "vue-cli-service build --mode local --report", "build:test": "vue-cli-service build --mode test --report", "build:prod": "vue-cli-service build --mode prod --report", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.2", "axios-form-data": "^1.1.1", "core-js": "^3.6.5", "crypto": "^1.0.1", "element-ui": "^2.13.2", "github-markdown-css": "^5.8.1", "highlight.js": "^11.11.1", "jszip": "^3.10.1", "less-loader": "^6.2.0", "lodash": "^4.17.21", "lowcodelib": "1.1.381", "lrz": "^4.9.40", "markdown-it": "^13.0.1", "markdown-it-vue": "^1.1.7", "md5": "^2.3.0", "print-js": "^1.5.0", "prismjs": "^1.30.0", "rangy": "^1.3.2", "sass": "1.32.6", "sass-loader": "^7.1.0", "save-as": "^0.1.8", "script-loader": "^0.7.2", "sortablejs": "^1.15.2", "spark-md5": "^3.0.2", "uuid-js": "^0.7.5", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-cropper": "^0.6.5", "vue-router": "^3.4.2", "vue-waterfall": "^1.0.6", "vuex": "^3.6.2", "xlsx": "^0.16.7"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-service": "~4.4.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "vue-template-compiler": "^2.6.11", "vue-waterfall-easy": "^2.4.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off", "no-console": "off", "no-debugger": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}